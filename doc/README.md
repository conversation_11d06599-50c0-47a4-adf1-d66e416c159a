# SSD制造测试工具 - 项目文档

## 📚 文档目录

### 🏗️ 架构设计文档
- **[系统架构概要设计](./SSD_Manufacturing_System_Architecture_Overview.md)**
  - 系统整体架构设计
  - 技术栈选择和模块划分
  - 数据库设计和接口定义
  - 部署和运行环境要求

### 🚀 开发计划文档
- **[迭代开发计划](./SSD_Manufacturing_Tool_Iterative_Development_Plan.md)**
  - 6个阶段的迭代开发计划
  - 每个阶段的具体开发任务
  - 代码示例和实现指导
  - 检查清单和交付物

## 📋 项目概述

**项目名称：** 企业级SSD量产命令行工具系统

**项目目标：**
- 支持FT1（硬件基础测试）和FT2（完整功能测试）
- 提供强大的命令行界面，支持脚本调用和批处理
- 实现本地化数据存储和日志管理
- 支持多设备并发测试
- 输出格式便于脚本解析（JSON、CSV、表格）

**技术栈：**
- **CLI框架：** Click + Rich
- **核心语言：** Python 3.9+
- **配置管理：** YAML + Pydantic
- **数据库：** SQLite + SQLAlchemy
- **硬件通信：** pySerial + PyNvme
- **打包工具：** PyInstaller

## 🎯 开发策略

采用**迭代开发**的方式，从最简单的可运行CLI开始，逐步添加功能：

1. **第一阶段（1周）：** 最简可运行CLI框架
2. **第二阶段（2周）：** 核心业务模块
3. **第三阶段（2周）：** FT1测试命令
4. **第四阶段（2周）：** FT2测试命令
5. **第五阶段（1周）：** 日志和批处理功能
6. **第六阶段（2周）：** 集成测试和优化

**总开发周期：** 约10周（2.5个月）

## 📁 项目结构预览

```
mp_tools/                     # 项目根目录
├── main.py                    # CLI应用入口
├── requirements.txt           # 依赖包列表
├── config.yaml               # 配置文件
├── setup.py                  # 包安装配置
├── doc/                      # 项目文档
│   ├── README.md             # 文档索引
│   ├── SSD_Manufacturing_System_Architecture_Overview.md
│   └── SSD_Manufacturing_Tool_Iterative_Development_Plan.md
├── cli/                      # 命令行接口模块
│   ├── __init__.py
│   ├── main.py               # 主命令入口
│   ├── commands/             # 命令模块
│   │   ├── __init__.py
│   │   ├── device.py         # 设备管理命令
│   │   ├── ft1.py            # FT1测试命令
│   │   ├── ft2.py            # FT2测试命令
│   │   ├── logs.py           # 日志管理命令
│   │   ├── batch.py          # 批处理命令
│   │   └── config.py         # 配置管理命令
│   ├── formatters/           # 输出格式化器
│   │   ├── __init__.py
│   │   ├── json_formatter.py
│   │   ├── csv_formatter.py
│   │   └── table_formatter.py
│   └── utils/                # CLI工具函数
│       ├── __init__.py
│       ├── progress.py       # 进度条显示
│       └── validators.py     # 参数验证
├── core/                     # 核心业务模块
│   ├── __init__.py
│   ├── ft1_engine.py         # FT1测试引擎
│   ├── ft2_engine.py         # FT2测试引擎
│   ├── data_manager.py       # 数据管理
│   ├── log_viewer.py         # 日志查看引擎
│   ├── device_manager.py     # 设备管理
│   └── batch_processor.py    # 批处理引擎
├── hardware/                 # 硬件接口模块
│   ├── __init__.py
│   ├── nvme_interface.py     # NVMe接口
│   ├── serial_interface.py   # 串口接口
│   ├── gpio_interface.py     # GPIO接口
│   └── device_detector.py    # 设备检测
├── models/                   # 数据模型
│   ├── __init__.py
│   └── database.py           # 数据库模型
├── utils/                    # 工具模块
│   ├── __init__.py
│   ├── logger.py             # 日志工具
│   └── helpers.py            # 辅助函数
├── scripts/                  # 脚本和工具
│   ├── build.py              # 打包脚本
│   ├── install.sh            # 安装脚本
│   └── examples/             # 使用示例
│       ├── batch_test.yaml   # 批处理配置示例
│       └── automation.sh     # 自动化脚本示例
├── tests/                    # 测试模块
│   ├── __init__.py
│   ├── test_ft1.py           # FT1测试用例
│   ├── test_ft2.py           # FT2测试用例
│   └── test_data.py          # 数据管理测试
└── data/                     # 数据目录（运行时创建）
    ├── database/             # 数据库文件
    ├── logs/                 # 日志文件
    └── config/               # 用户配置文件
```

## 🚦 当前状态

- ✅ 系统架构设计完成（CLI版本）
- ✅ 迭代开发计划制定完成
- ⏳ 准备开始第一阶段开发
- ⏳ 基础项目结构创建
- ⏳ 最简CLI框架开发

## 📞 联系信息

**项目负责人：** [待填写]  
**开发团队：** [待填写]  
**项目开始时间：** 2025-07-30

---

**文档版本：** v1.0
**最后更新：** 2025-07-30
