# SSD制造测试工具 - 迭代开发计划

## 📋 开发策略

**核心理念：** 从最简可运行界面开始，逐步迭代添加功能，每个阶段都能产出可打包的软件。

**技术栈：** Python 3.9+ + PySide6 + SQLite + PyInstaller

---

## 🚀 第一阶段：最简可运行界面（1周）

### 🎯 阶段目标
创建一个可以打包运行的基础桌面应用，包含4个空标签页和基础框架。

### 📁 项目结构
```
ssd_manufacturing_tool/
├── main.py                    # 应用入口
├── requirements.txt           # 基础依赖
├── config.json               # 配置文件
├── gui/
│   ├── __init__.py
│   └── main_window.py        # 主窗口
├── utils/
│   ├── __init__.py
│   └── logger.py             # 简单日志
└── build/
    └── build_spec.py         # PyInstaller配置
```

### 🔧 核心开发任务

#### 1. 主窗口框架 (`gui/main_window.py`)
```python
from PySide6.QtWidgets import (QMainWindow, QVBoxLayout, QWidget, 
                               QTabWidget, QLabel, QPushButton, 
                               QTextEdit, QStatusBar)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SSD制造测试工具 v0.1")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建标签页
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)
        
        # 添加基础标签页
        self.add_overview_tab()
        self.add_ft1_tab()
        self.add_ft2_tab()
        self.add_logs_tab()
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def add_overview_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        welcome_label = QLabel("欢迎使用SSD制造测试工具")
        welcome_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        layout.addWidget(welcome_label)
        
        status_label = QLabel("系统状态: 正常")
        layout.addWidget(status_label)
        
        widget.setLayout(layout)
        self.tabs.addTab(widget, "总览")
    
    def add_ft1_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        title_label = QLabel("FT1 硬件基础测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(title_label)
        
        placeholder_label = QLabel("FT1测试功能开发中...")
        layout.addWidget(placeholder_label)
        
        widget.setLayout(layout)
        self.tabs.addTab(widget, "FT1测试")
    
    def add_ft2_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        title_label = QLabel("FT2 完整功能测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(title_label)
        
        placeholder_label = QLabel("FT2测试功能开发中...")
        layout.addWidget(placeholder_label)
        
        widget.setLayout(layout)
        self.tabs.addTab(widget, "FT2测试")
    
    def add_logs_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        title_label = QLabel("日志查看")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(title_label)
        
        log_text = QTextEdit()
        log_text.setPlainText("日志功能开发中...\n这里将显示系统日志信息")
        log_text.setReadOnly(True)
        layout.addWidget(log_text)
        
        widget.setLayout(layout)
        self.tabs.addTab(widget, "日志查看")
```

#### 2. 应用入口 (`main.py`)
```python
import sys
import os
from PySide6.QtWidgets import QApplication
from gui.main_window import MainWindow
from utils.logger import setup_logger

def main():
    # 设置日志
    setup_logger()
    
    # 创建应用
    app = QApplication(sys.argv)
    app.setApplicationName("SSD制造测试工具")
    app.setApplicationVersion("0.1.0")
    
    # 设置应用图标（可选）
    # app.setWindowIcon(QIcon("icon.ico"))
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
```

#### 3. 基础日志工具 (`utils/logger.py`)
```python
import logging
import os
from datetime import datetime

def setup_logger():
    """设置基础日志配置"""
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置日志文件
    log_filename = f"logs/app_{datetime.now().strftime('%Y%m%d')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("应用程序启动")
    return logger
```

#### 4. 基础依赖 (`requirements.txt`)
```
PySide6>=6.5.0
```

#### 5. 配置文件 (`config.json`)
```json
{
    "app": {
        "name": "SSD制造测试工具",
        "version": "0.1.0",
        "window": {
            "width": 1200,
            "height": 800,
            "title": "SSD制造测试工具"
        }
    },
    "logging": {
        "level": "INFO",
        "file_rotation": true
    }
}
```

#### 6. 打包配置 (`build/build_spec.py`)
```python
"""
PyInstaller打包配置脚本
使用方法: python build/build_spec.py
"""
import PyInstaller.__main__
import os

def build_app():
    PyInstaller.__main__.run([
        'main.py',
        '--onefile',
        '--windowed',
        '--name=SSD_Manufacturing_Tool_v0.1',
        '--add-data=config.json;.',
        '--distpath=dist',
        '--workpath=build/temp',
        '--specpath=build',
        '--clean',
        # '--icon=assets/icon.ico',  # 如果有图标文件
    ])

if __name__ == "__main__":
    build_app()
    print("打包完成！可执行文件位于 dist/ 目录中")
```

### ✅ 第一阶段检查清单

- [ ] 1. 创建基础项目结构
- [ ] 2. 实现最简主窗口（4个空标签页）
- [ ] 3. 添加基础日志功能
- [ ] 4. 创建配置文件管理
- [ ] 5. 配置PyInstaller打包
- [ ] 6. 测试打包后的exe文件能正常运行
- [ ] 7. 验证界面基本交互功能

### 📦 第一阶段交付物
- 一个可运行的桌面应用程序（约5-10MB的exe文件）
- 基础的4标签页界面
- 简单的日志系统
- 完整的打包配置

---

## 🚀 第二阶段：添加基础数据管理（1周）

### 🎯 阶段目标
添加SQLite数据库和基础数据显示功能，实现产品信息的增删改查。

### 📁 新增模块结构
```
├── models/
│   ├── __init__.py
│   └── database.py           # 数据库模型
├── core/
│   ├── __init__.py
│   └── data_manager.py       # 数据管理器
```

### 🔧 核心开发任务

#### 1. 数据库模型 (`models/database.py`)
```python
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

Base = declarative_base()

class Product(Base):
    __tablename__ = 'products'
    id = Column(Integer, primary_key=True)
    serial_number = Column(String(50), unique=True)
    model = Column(String(50))
    capacity = Column(String(20))
    current_stage = Column(String(10), default='READY')  # READY/FT1/FT2/COMPLETE
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class TestRecord(Base):
    __tablename__ = 'test_records'
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer)
    test_stage = Column(String(10))  # FT1/FT2
    test_result = Column(String(10))  # PASS/FAIL
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    notes = Column(String(500))
```

#### 2. 数据管理器 (`core/data_manager.py`)
```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.database import Base, Product, TestRecord
import os

class DataManager:
    def __init__(self, db_path="data/manufacturing.db"):
        # 确保数据目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        self.engine = create_engine(f'sqlite:///{db_path}')
        Base.metadata.create_all(self.engine)
        
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
    
    def add_product(self, serial_number, model, capacity):
        """添加新产品"""
        product = Product(
            serial_number=serial_number,
            model=model,
            capacity=capacity
        )
        self.session.add(product)
        self.session.commit()
        return product
    
    def get_all_products(self):
        """获取所有产品"""
        return self.session.query(Product).all()
    
    def update_product_stage(self, product_id, stage):
        """更新产品阶段"""
        product = self.session.query(Product).filter_by(id=product_id).first()
        if product:
            product.current_stage = stage
            self.session.commit()
        return product
```

#### 3. 更新总览标签页显示产品列表

### ✅ 第二阶段检查清单

- [ ] 8. 集成SQLite数据库
- [ ] 9. 创建基础数据模型
- [ ] 10. 在界面中显示产品列表
- [ ] 11. 实现产品的增删改查
- [ ] 12. 更新打包配置包含数据库
- [ ] 13. 测试数据持久化功能

---

## 🚀 第三阶段：添加日志查看功能（1周）

### 🎯 阶段目标
实现完整的日志查看系统，包括日志过滤、搜索和导出功能。

### ✅ 第三阶段检查清单

- [ ] 14. 实现日志记录表
- [ ] 15. 开发日志查看界面
- [ ] 16. 添加日志过滤功能
- [ ] 17. 实现日志搜索
- [ ] 18. 添加日志导出功能
- [ ] 19. 测试大量日志数据的性能

---

## 🚀 第四阶段：添加FT1基础功能（2周）

### 🎯 阶段目标
实现FT1测试的基础框架，包括模拟的串口通信和测试流程。

### ✅ 第四阶段检查清单

- [ ] 20. 开发串口接口（先用模拟数据）
- [ ] 21. 实现FT1测试界面
- [ ] 22. 添加测试流程控制
- [ ] 23. 实现测试结果存储
- [ ] 24. 添加测试进度显示

---

## 🚀 第五阶段：添加FT2基础功能（2周）

### 🎯 阶段目标
实现FT2测试的基础框架，包括NVMe设备管理模拟。

### ✅ 第五阶段检查清单

- [ ] 25. 集成PyNvme库（或模拟）
- [ ] 26. 开发FT2测试界面
- [ ] 27. 实现NVMe设备管理
- [ ] 28. 添加测试数据显示

---

## 🚀 第六阶段：硬件集成和优化（3周）

### 🎯 阶段目标
集成真实硬件接口，完善系统稳定性和性能。

### ✅ 第六阶段检查清单

- [ ] 29. 集成真实串口通信
- [ ] 30. 集成GPIO控制
- [ ] 31. 集成真实PyNvme
- [ ] 32. 性能优化和稳定性测试
- [ ] 33. 完善异常处理
- [ ] 34. 用户文档编写

---

## 📊 开发时间线

| 阶段 | 时间 | 主要交付物 | 关键里程碑 |
|------|------|------------|------------|
| 第一阶段 | 第1周 | 基础GUI应用 | 可打包运行的桌面应用 |
| 第二阶段 | 第2周 | 数据管理功能 | 产品信息管理 |
| 第三阶段 | 第3周 | 日志查看系统 | 完整日志功能 |
| 第四阶段 | 第4-5周 | FT1测试框架 | FT1基础功能 |
| 第五阶段 | 第6-7周 | FT2测试框架 | FT2基础功能 |
| 第六阶段 | 第8-10周 | 完整系统 | 生产就绪版本 |

**总开发周期：约10周（2.5个月）**

---

## 🛠️ 开发环境要求

### 基础环境
- Python 3.9+
- Git
- 代码编辑器（推荐VSCode）

### Python依赖
```bash
pip install PySide6 SQLAlchemy PyInstaller
```

### 开发工具
- PyInstaller（打包）
- pytest（测试）
- black（代码格式化）

---

## 📝 注意事项

1. **每个阶段结束都要能打包成可运行的软件**
2. **优先保证功能完整性，再考虑性能优化**
3. **使用模拟数据进行早期开发，降低硬件依赖**
4. **保持代码简洁，便于后续迭代**
5. **及时记录开发过程中的问题和解决方案**

---

**文档版本**: v1.0  
**创建日期**: 2025-01-30  
**最后更新**: 2025-01-30
