# SSD制造测试CLI工具 - 迭代开发计划

## 📋 开发策略

**核心理念：** 从最简可运行CLI开始，逐步迭代添加功能，每个阶段都能产出可打包的命令行工具。

**技术栈：** Python 3.9+ + Click + Rich + SQLite + PyInstaller

---

## 🚀 第一阶段：最简可运行CLI（1周）

### 🎯 阶段目标
创建一个可以打包运行的基础命令行工具，包含基本命令结构和帮助系统。

### 📁 项目结构
```
mp_tools/                      # 项目根目录
├── main.py                    # CLI应用入口
├── requirements.txt           # 基础依赖
├── config.yaml               # 默认配置文件
├── setup.py                  # 包安装配置
├── doc/                      # 项目文档
│   ├── README.md
│   ├── SSD_Manufacturing_System_Architecture_Overview.md
│   └── SSD_Manufacturing_Tool_Iterative_Development_Plan.md
├── cli/
│   ├── __init__.py
│   ├── main.py               # 主命令入口
│   └── commands/             # 命令模块目录
│       └── __init__.py
├── utils/
│   ├── __init__.py
│   └── logger.py             # 简单日志
└── scripts/
    └── build.py              # 打包脚本
```

### 🔧 核心开发任务

#### 1. 主命令入口 (`cli/main.py`)
```python
import click
from rich.console import Console
from rich.table import Table

console = Console()

@click.group()
@click.option('--config', '-c', default='config.yaml', help='配置文件路径')
@click.option('--output-format', '-f', default='table',
              type=click.Choice(['json', 'csv', 'table']), help='输出格式')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
@click.option('--quiet', '-q', is_flag=True, help='静默模式')
@click.pass_context
def cli(ctx, config, output_format, verbose, quiet):
    """SSD制造测试CLI工具 v0.1"""
    ctx.ensure_object(dict)
    ctx.obj['config'] = config
    ctx.obj['output_format'] = output_format
    ctx.obj['verbose'] = verbose
    ctx.obj['quiet'] = quiet

    if not quiet:
        console.print("[bold green]SSD制造测试工具 v0.1[/bold green]")

@cli.group()
def device():
    """设备管理命令"""
    pass

@cli.group()
def ft1():
    """FT1硬件基础测试命令"""
    pass

@cli.group()
def ft2():
    """FT2完整功能测试命令"""
    pass

@cli.group()
def logs():
    """日志管理命令"""
    pass

@cli.group()
def batch():
    """批处理命令"""
    pass

@cli.group()
def config():
    """配置管理命令"""
    pass

# 基础命令实现
@device.command()
@click.pass_context
def list(ctx):
    """列出所有设备"""
    console.print("[yellow]设备列表功能开发中...[/yellow]")

    # 示例输出
    table = Table(title="SSD设备列表")
    table.add_column("设备ID", style="cyan")
    table.add_column("状态", style="green")
    table.add_column("型号", style="blue")

    table.add_row("DEV001", "就绪", "示例设备")
    console.print(table)

@ft1.command()
@click.argument('device_id')
@click.pass_context
def start(ctx, device_id):
    """启动FT1测试"""
    console.print(f"[green]启动设备 {device_id} 的FT1测试...[/green]")
    console.print("[yellow]FT1测试功能开发中...[/yellow]")

@ft2.command()
@click.argument('device_id')
@click.pass_context
def start(ctx, device_id):
    """启动FT2测试"""
    console.print(f"[green]启动设备 {device_id} 的FT2测试...[/green]")
    console.print("[yellow]FT2测试功能开发中...[/yellow]")

@logs.command()
@click.option('--type', '-t', help='日志类型')
@click.option('--device', '-d', help='设备ID')
@click.pass_context
def view(ctx, type, device):
    """查看日志"""
    console.print("[green]日志查看功能[/green]")
    console.print("[yellow]日志功能开发中...[/yellow]")
    console.print("这里将显示系统日志信息")

if __name__ == '__main__':
    cli()
```

#### 2. 应用入口 (`main.py`)
```python
#!/usr/bin/env python3
"""
SSD制造测试CLI工具主入口
"""
import sys
import os
from cli.main import cli
from utils.logger import setup_logger

def main():
    """主函数"""
    try:
        # 设置日志
        setup_logger()

        # 启动CLI应用
        cli()

    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"应用启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

#### 3. 基础日志工具 (`utils/logger.py`)
```python
import logging
import os
from datetime import datetime

def setup_logger():
    """设置基础日志配置"""
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置日志文件
    log_filename = f"logs/app_{datetime.now().strftime('%Y%m%d')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("应用程序启动")
    return logger
```

#### 4. 基础依赖 (`requirements.txt`)
```
click>=8.0.0
rich>=13.0.0
pydantic>=2.0.0
PyYAML>=6.0
```

#### 5. 配置文件 (`config.yaml`)
```yaml
# SSD制造测试工具配置文件
app:
  name: "SSD制造测试CLI工具"
  version: "0.1.0"

logging:
  level: "INFO"
  file_rotation: true
  max_file_size: "10MB"
  backup_count: 5

database:
  path: "data/database/manufacturing.db"

hardware:
  serial_timeout: 5
  gpio_pins:
    activity: 18

output:
  default_format: "table"
  colors: true
  progress_bars: true
```

#### 6. 包安装配置 (`setup.py`)
```python
"""
SSD制造测试CLI工具安装配置
"""
from setuptools import setup, find_packages

setup(
    name="ssd-manufacturing-tool",
    version="0.1.0",
    description="企业级SSD量产命令行工具",
    packages=find_packages(),
    install_requires=[
        "click>=8.0.0",
        "rich>=13.0.0",
        "pydantic>=2.0.0",
        "PyYAML>=6.0",
    ],
    entry_points={
        'console_scripts': [
            'ssd-tool=main:main',
        ],
    },
    python_requires=">=3.9",
)
```

#### 7. 打包脚本 (`scripts/build.py`)
```python
"""
CLI工具打包脚本
使用方法: python scripts/build.py
"""
import PyInstaller.__main__
import os
import shutil

def build_cli():
    """打包CLI工具"""
    PyInstaller.__main__.run([
        'main.py',
        '--onefile',
        '--console',  # CLI工具使用控制台模式
        '--name=ssd-tool',
        '--add-data=config.yaml;.',
        '--distpath=dist',
        '--workpath=build/temp',
        '--specpath=build',
        '--clean',
    ])

    print("✅ CLI工具打包完成！")
    print("📁 可执行文件位于: dist/ssd-tool")
    print("🚀 使用方法: ./dist/ssd-tool --help")

if __name__ == "__main__":
    build_cli()
```

### ✅ 第一阶段检查清单

- [ ] 1. 创建基础项目结构
- [ ] 2. 实现CLI主命令入口和基础命令组
- [ ] 3. 添加基础日志功能
- [ ] 4. 创建YAML配置文件管理
- [ ] 5. 配置PyInstaller打包（控制台模式）
- [ ] 6. 测试打包后的可执行文件能正常运行
- [ ] 7. 验证命令行参数解析和帮助系统
- [ ] 8. 实现基础的输出格式化（表格显示）

### 📦 第一阶段交付物
- 一个可运行的CLI工具（约3-5MB的可执行文件）
- 完整的命令行界面框架（6个命令组）
- 美观的终端输出（Rich库支持）
- YAML配置文件系统
- 完整的打包配置

---

## 🚀 第二阶段：添加核心业务模块（2周）

### 🎯 阶段目标
添加SQLite数据库、设备管理和基础业务逻辑，实现完整的数据管理和设备操作命令。

### 📁 新增模块结构
```
mp_tools/                      # 项目根目录
├── models/
│   ├── __init__.py
│   └── database.py           # 数据库模型
├── core/
│   ├── __init__.py
│   ├── data_manager.py       # 数据管理器
│   └── device_manager.py     # 设备管理器
├── cli/
│   └── commands/
│       ├── device.py         # 设备管理命令（完善）
│       └── config.py         # 配置管理命令（完善）
└── data/                     # 数据目录（新增）
    └── manufacturing.db      # SQLite数据库文件
```

### 🔧 核心开发任务

#### 1. 数据库模型 (`models/database.py`)
```python
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

Base = declarative_base()

class Product(Base):
    __tablename__ = 'products'
    id = Column(Integer, primary_key=True)
    serial_number = Column(String(50), unique=True)
    model = Column(String(50))
    capacity = Column(String(20))
    current_stage = Column(String(10), default='READY')  # READY/FT1/FT2/COMPLETE
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class TestRecord(Base):
    __tablename__ = 'test_records'
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer)
    test_stage = Column(String(10))  # FT1/FT2
    test_result = Column(String(10))  # PASS/FAIL
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    notes = Column(String(500))
```

#### 2. 数据管理器 (`core/data_manager.py`)
```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.database import Base, Product, TestRecord
import os

class DataManager:
    def __init__(self, db_path="data/manufacturing.db"):
        # 确保数据目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        self.engine = create_engine(f'sqlite:///{db_path}')
        Base.metadata.create_all(self.engine)
        
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
    
    def add_product(self, serial_number, model, capacity):
        """添加新产品"""
        product = Product(
            serial_number=serial_number,
            model=model,
            capacity=capacity
        )
        self.session.add(product)
        self.session.commit()
        return product
    
    def get_all_products(self):
        """获取所有产品"""
        return self.session.query(Product).all()
    
    def update_product_stage(self, product_id, stage):
        """更新产品阶段"""
        product = self.session.query(Product).filter_by(id=product_id).first()
        if product:
            product.current_stage = stage
            self.session.commit()
        return product
```

#### 3. 更新总览标签页显示产品列表

### ✅ 第二阶段检查清单

- [ ] 9. 集成SQLite数据库和ORM
- [ ] 10. 创建完整的数据模型
- [ ] 11. 实现设备管理命令（list, info, scan）
- [ ] 12. 实现配置管理命令（show, set, validate）
- [ ] 13. 添加JSON/CSV输出格式支持
- [ ] 14. 实现数据持久化和查询功能
- [ ] 15. 更新打包配置包含数据库

---

## 🚀 第三阶段：FT1测试命令开发（2周）

### 🎯 阶段目标
实现完整的FT1测试命令，包括串口通信、GPIO控制和测试流程管理。

### ✅ 第三阶段检查清单

- [ ] 16. 开发串口接口模块（先用模拟数据）
- [ ] 17. 实现GPIO控制模块
- [ ] 18. 开发FT1测试引擎
- [ ] 19. 实现FT1相关CLI命令（start, status, result）
- [ ] 20. 添加测试进度显示（Rich Progress）
- [ ] 21. 实现测试结果存储和查询
- [ ] 22. 添加错误处理和重试机制

---

## 🚀 第四阶段：FT2测试命令开发（2周）

### 🎯 阶段目标
实现完整的FT2测试命令，包括NVMe设备管理和测试流程。

### ✅ 第四阶段检查清单

- [ ] 23. 集成PyNvme库（或开发模拟器）
- [ ] 24. 开发NVMe设备管理模块
- [ ] 25. 实现FT2测试引擎
- [ ] 26. 实现FT2相关CLI命令（start, status, result）
- [ ] 27. 添加burn-in测试和SLT测试支持
- [ ] 28. 实现产品信息写入功能

---

## 🚀 第五阶段：日志和批处理功能（1周）

### 🎯 阶段目标
实现完整的日志管理和批处理自动化功能。

### ✅ 第五阶段检查清单

- [ ] 29. 实现日志查看和过滤命令
- [ ] 30. 添加日志导出功能（CSV, JSON）
- [ ] 31. 开发批处理引擎
- [ ] 32. 实现批处理配置文件支持（YAML）
- [ ] 33. 添加并行处理支持
- [ ] 34. 实现批处理进度监控

---

## 🚀 第六阶段：集成测试和优化（2周）

### 🎯 阶段目标
集成真实硬件接口，完善系统稳定性、性能和文档。

### ✅ 第六阶段检查清单

- [ ] 35. 集成真实串口通信和GPIO控制
- [ ] 36. 集成真实PyNvme库
- [ ] 37. 性能优化和内存管理
- [ ] 38. 完善异常处理和错误恢复
- [ ] 39. 编写完整的CLI用户手册
- [ ] 40. 创建自动化脚本示例
- [ ] 41. 系统集成测试和验证
- [ ] 42. 准备生产部署配置

---

## 📊 开发时间线

| 阶段 | 时间 | 主要交付物 | 关键里程碑 |
|------|------|------------|------------|
| 第一阶段 | 第1周 | 基础CLI框架 | 可打包运行的命令行工具 |
| 第二阶段 | 第2-3周 | 核心业务模块 | 设备管理和数据存储 |
| 第三阶段 | 第4-5周 | FT1测试命令 | 完整FT1测试功能 |
| 第四阶段 | 第6-7周 | FT2测试命令 | 完整FT2测试功能 |
| 第五阶段 | 第8周 | 日志和批处理 | 自动化和日志管理 |
| 第六阶段 | 第9-10周 | 集成优化 | 生产就绪版本 |

**总开发周期：约10周（2.5个月）**

---

## 🛠️ 开发环境要求

### 基础环境
- Python 3.9+
- Git
- 代码编辑器（推荐VSCode）

### Python依赖
```bash
pip install click rich pydantic PyYAML SQLAlchemy PyInstaller
```

### 开发工具
- PyInstaller（CLI打包）
- pytest（测试）
- black（代码格式化）
- mypy（类型检查）

---

## 📝 注意事项

1. **每个阶段结束都要能打包成可运行的CLI工具**
2. **优先保证命令行接口的易用性和一致性**
3. **使用模拟数据进行早期开发，降低硬件依赖**
4. **保持命令结构清晰，便于脚本调用**
5. **重视输出格式的标准化，便于自动化集成**
6. **及时记录CLI使用示例和最佳实践**

---

**文档版本**: v1.0
**创建日期**: 2025-07-30
**最后更新**: 2025-07-30
