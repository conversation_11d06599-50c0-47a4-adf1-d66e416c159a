# 企业级SSD量产命令行工具系统 - 概要设计文档

## 1. 系统概述

### 1.1 项目背景
本系统是一个企业级SSD量产命令行工具(CLI)。支持FT1（硬件基础测试）和FT2（完整功能测试）两个关键测试阶段，实现从硬件验证到最终产品交付的全流程自动化管理。主要部署在Linux服务器环境，支持无图形界面操作和批处理自动化。

### 1.2 核心目标
- 提供强大的命令行界面，支持脚本调用和批处理
- 自动化管理SSD生产测试全流程
- 本地化数据存储，确保测试数据安全可靠
- 支持多设备并发测试和实时状态监控
- 提供完整的日志查看和历史查询功能
- 输出格式便于脚本解析（JSON、CSV、表格）

## 2. 系统架构总览

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "命令行接口层"
        A1[主命令入口]
        A2[参数解析器]
        A3[命令路由器]
        A4[输出格式化器]
    end

    subgraph "应用控制层"
        B1[命令处理器]
        B2[配置管理器]
        B3[批处理控制器]
        B4[进度管理器]
    end

    subgraph "核心业务层"
        C1[FT1测试引擎]
        C2[FT2测试引擎]
        C3[数据管理引擎]
        C4[日志查看引擎]
    end

    subgraph "硬件接口层"
        D1[PyNvme/SPDK接口]
        D2[串口通信模块]
        D3[GPIO控制模块]
        D4[设备管理模块]
    end

    subgraph "数据存储层"
        E1[SQLite本地数据库]
        E2[配置文件存储]
        E3[日志文件存储]
    end

    A1 --> A2
    A2 --> A3
    A3 --> B1
    A4 --> B1

    B1 --> C1
    B1 --> C2
    B2 --> C3
    B3 --> C4

    C1 --> D2
    C1 --> D3
    C2 --> D1
    C3 --> E1
    C4 --> E3

    D1 --> E1
    D2 --> E3
    D3 --> E3
    D4 --> E1
```

### 2.2 技术栈选择

| 层级 | 技术选择 | 选择理由 |
|------|----------|----------|
| CLI框架 | Click + Rich | 强大的命令行解析和美观的终端输出 |
| 核心语言 | Python 3.9+ | 与PyNvme完美集成，开发效率高 |
| 配置管理 | YAML + Pydantic | 人性化配置格式，强类型验证 |
| 输出格式 | JSON + CSV + 表格 | 便于脚本解析和人工阅读 |
| 进度显示 | Rich Progress | 美观的进度条和状态显示 |
| 测试框架 | PyNvme + SPDK | SSD测试行业标准，成熟稳定 |
| 本地数据库 | SQLite + SQLAlchemy | 轻量级，无需额外服务，事务支持 |
| 硬件通信 | pySerial + RPi.GPIO | 串口和GPIO硬件接口标准库 |
| 数据处理 | pandas + numpy | 高效数据分析和处理 |
| 多线程 | asyncio + concurrent.futures | 支持异步操作和并发测试 |

## 3. 核心模块设计

### 3.1 命令行接口模块
**主要命令组件：**
- **主命令入口**: 统一的命令行入口点，全局参数处理
- **参数解析器**: 基于Click的命令和参数解析
- **命令路由器**: 将解析后的命令路由到对应的处理器
- **输出格式化器**: 支持JSON、CSV、表格等多种输出格式

**CLI特性：**
- 丰富的命令行参数和选项
- 美观的进度条和状态显示（Rich库）
- 支持管道操作和脚本调用
- 彩色输出和错误提示
- 自动补全支持

**命令结构设计：**
```bash
# 设备管理命令
ssd-tool device list [--format json|csv|table]
ssd-tool device info DEVICE_ID [--verbose]
ssd-tool device scan [--timeout SECONDS]

# FT1测试命令
ssd-tool ft1 start DEVICE_ID [--config CONFIG_FILE]
ssd-tool ft1 status DEVICE_ID [--watch]
ssd-tool ft1 result DEVICE_ID [--format json|csv]

# FT2测试命令
ssd-tool ft2 start DEVICE_ID [--config CONFIG_FILE]
ssd-tool ft2 status DEVICE_ID [--watch]
ssd-tool ft2 result DEVICE_ID [--format json|csv]

# 日志管理命令
ssd-tool logs view [--type TYPE] [--device DEVICE_ID] [--since TIME]
ssd-tool logs export [--format csv|json] [--output FILE]

# 批处理命令
ssd-tool batch run BATCH_FILE [--parallel N]
ssd-tool batch status JOB_ID

# 配置管理命令
ssd-tool config show [--section SECTION]
ssd-tool config set KEY VALUE
ssd-tool config validate [CONFIG_FILE]
```

### 3.2 FT1测试引擎
**核心功能：**
- 固件烧写管理（支持两种方案）
- ACTIVITY管脚状态监控和解析
- 串口通信和日志采集
- 硬件测试结果判定
- 测试流程状态机管理

**关键类设计：**
```python
class FT1TestEngine:
    def start_firmware_flash(self, method: str, device_id: str) -> bool
    def monitor_activity_pin(self, device_id: str) -> ActivityStatus
    def parse_test_result(self, activity_pattern: str) -> TestResult
    def collect_serial_logs(self, port: str) -> List[str]
    def execute_hardware_test(self, device_id: str) -> TestReport
```

### 3.3 FT2测试引擎
**核心功能：**
- NVMe设备连接和管理
- 测试固件下载和执行
- NAND burn-in测试控制
- SLT系统级测试管理
- 产品信息写入和开卡

**关键类设计：**
```python
class FT2TestEngine:
    def connect_nvme_device(self, device_path: str) -> NVMeDevice
    def download_test_firmware(self, device: NVMeDevice, firmware_path: str) -> bool
    def execute_burnin_test(self, device: NVMeDevice, duration: int) -> BurninResult
    def run_slt_test(self, device: NVMeDevice) -> SLTResult
    def write_product_info(self, device: NVMeDevice, product_info: dict) -> bool
```

### 3.4 数据管理引擎
**功能职责：**
- 本地SQLite数据库管理
- 测试数据实时存储和查询
- 日志数据管理和分析
- 坏块表管理和分析
- 产品信息和序列号管理
- 数据备份和恢复

**数据模型：**
```python
# 产品信息表
class Product(Base):
    __tablename__ = 'products'
    id = Column(Integer, primary_key=True)
    serial_number = Column(String(50), unique=True)
    model = Column(String(50))
    capacity = Column(String(20))
    manufacture_date = Column(DateTime)
    current_stage = Column(String(10))  # FT1/FT2/COMPLETE

# 测试记录表
class TestRecord(Base):
    __tablename__ = 'test_records'
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'))
    test_stage = Column(String(10))  # FT1/FT2
    test_result = Column(String(10))  # PASS/FAIL
    test_data = Column(JSON)
    start_time = Column(DateTime)
    end_time = Column(DateTime)

# 坏块表
class BadBlockTable(Base):
    __tablename__ = 'bad_blocks'
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'))
    block_address = Column(BigInteger)
    block_type = Column(String(20))
    detected_stage = Column(String(10))
    severity = Column(String(10))

# 日志记录表
class LogRecord(Base):
    __tablename__ = 'log_records'
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'))
    log_type = Column(String(20))  # TEST/OPERATION/ERROR/HARDWARE
    log_level = Column(String(10))  # DEBUG/INFO/WARNING/ERROR
    message = Column(Text)
    device_id = Column(String(50))
    timestamp = Column(DateTime)
    details = Column(JSON)
```

### 3.5 日志查看系统
**功能职责：**
- 多类型日志查看（测试日志、操作日志、错误日志、硬件通信日志）
- 历史日志查询和检索
- 日志过滤和搜索（按时间、设备ID、日志级别、关键词）
- 实时日志监控和显示
- 日志数据导出功能

**关键类设计：**
```python
class LogViewerEngine:
    def get_logs_by_type(self, log_type: str, start_time: datetime, end_time: datetime) -> List[LogRecord]
    def search_logs(self, keyword: str, filters: dict) -> List[LogRecord]
    def filter_logs(self, device_id: str, log_level: str, time_range: tuple) -> List[LogRecord]
    def export_logs(self, logs: List[LogRecord], format: str) -> str
    def get_real_time_logs(self, callback: callable) -> None
```

## 4. 应用架构设计

### 4.1 多线程架构

```mermaid
graph TD
    A[主CLI进程<br/>命令处理] --> B[测试控制线程<br/>FT1/FT2测试执行]
    A --> C[硬件监控线程<br/>设备状态监控]
    A --> D[数据处理线程<br/>数据库操作]
    A --> E[日志记录线程<br/>日志文件写入]

    B --> F[FT1引擎]
    B --> G[FT2引擎]
    C --> H[设备状态监控]
    D --> I[SQLite数据库]
    E --> J[日志文件系统]
```

### 4.2 FT1测试流程

```mermaid
flowchart LR
    A[设备检测] --> B[固件烧写]
    B --> C[硬件测试启动]
    C --> D[ACTIVITY管脚监控]
    D --> E[结果解析]
    E --> F[数据存储]
    F --> G[状态更新]
    G --> H[流转到组装环节]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#e0f2f1
    style G fill:#f1f8e9
    style H fill:#e8eaf6
```

### 4.3 FT2测试流程

```mermaid
flowchart LR
    A[NVMe设备连接] --> B[FT固件下载]
    B --> C[NAND burn-in测试]
    C --> D[SLT系统测试]
    D --> E[产品信息写入]
    E --> F[最终验证]
    F --> G[测试完成]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#e0f2f1
    style G fill:#f1f8e9
```

### 4.4 状态管理机制
- **设备状态**: 空闲/测试中/完成/异常
- **测试状态**: 未开始/进行中/暂停/完成/失败
- **数据状态**: 实时同步到本地数据库
- **命令状态**: 实时输出到终端和状态文件

## 5. 关键技术方案

### 5.1 多设备并发支持
- **QThread多线程**: 每个测试设备独立线程
- **设备资源池**: 统一管理串口、NVMe设备资源
- **任务队列**: 支持测试任务排队和调度
- **状态同步**: 线程安全的状态共享机制

### 5.2 实时监控技术
- **异步处理**: asyncio跨线程数据传递
- **定时器机制**: 定期状态检查和更新
- **事件驱动**: 硬件状态变化触发状态更新
- **状态机**: 测试流程状态管理
- **进度显示**: Rich Progress实时进度条

### 5.3 数据持久化
- **SQLite事务**: 保证数据一致性
- **连接池**: 数据库连接复用
- **批量操作**: 提高数据写入效率
- **自动备份**: 定期数据备份机制

### 5.4 异常处理和恢复
- **分层异常处理**: CLI/业务/硬件层异常分离
- **自动重试**: 硬件通信失败自动重试
- **故障恢复**: 测试中断后状态恢复
- **日志记录**: 完整的操作和错误日志
- **优雅退出**: 信号处理和资源清理

### 5.5 安全性设计
- **用户权限**: 操作员/管理员权限分离
- **数据完整性**: 测试数据防篡改机制
- **操作审计**: 关键操作日志记录
- **配置保护**: 重要配置文件加密存储

## 6. 部署和运行环境

### 6.1 系统要求
- **操作系统**: Ubuntu 20.04+, CentOS 8+, RHEL 8+ (主要), Windows 10/11 (兼容)
- **Python版本**: Python 3.9+
- **内存要求**: 最低2GB，推荐4GB+
- **存储空间**: 最低5GB可用空间
- **硬件接口**: USB串口、PCIe NVMe插槽
- **终端要求**: 支持ANSI颜色和Unicode字符

### 6.2 依赖环境
```bash
# CLI核心依赖
click>=8.0.0
rich>=13.0.0
pydantic>=2.0.0
PyYAML>=6.0

# 业务核心依赖
PyNvme>=3.0
pySerial>=3.5
SQLAlchemy>=2.0
pandas>=1.5.0
numpy>=1.24.0

# 硬件控制依赖
RPi.GPIO>=0.7.1  # 树莓派GPIO控制
pyftdi>=0.54.0   # FTDI设备支持

# 开发和打包依赖
pytest>=7.0.0
PyInstaller>=5.0.0
```

### 6.3 安装部署
- **安装方式**: pip安装或PyInstaller打包的独立可执行文件
- **配置管理**: YAML配置文件，支持环境变量覆盖
- **数据目录**: 自动创建用户数据目录 (~/.ssd-tool/)
- **日志管理**: 按日期和大小轮转的日志文件
- **系统集成**: 支持systemd服务和cron任务

## 7. 项目目录结构

### 7.1 推荐项目结构

```
mp_tools/                      # CLI工具项目根目录
├── 📄 main.py                 # CLI应用程序入口
├── 📄 requirements.txt        # 依赖包列表
├── 📄 setup.py               # 包安装配置
├── 📄 config.yaml            # 默认配置文件
├── 📁 cli/                    # 命令行接口模块
│   ├── __init__.py
│   ├── main.py             # 主命令入口
│   ├── commands/           # 命令模块
│   │   ├── __init__.py
│   │   ├── device.py       # 设备管理命令
│   │   ├── ft1.py          # FT1测试命令
│   │   ├── ft2.py          # FT2测试命令
│   │   ├── logs.py         # 日志管理命令
│   │   ├── batch.py        # 批处理命令
│   │   └── config.py       # 配置管理命令
│   ├── formatters/         # 输出格式化器
│   │   ├── __init__.py
│   │   ├── json_formatter.py
│   │   ├── csv_formatter.py
│   │   └── table_formatter.py
│   └── utils/              # CLI工具函数
│       ├── __init__.py
│       ├── progress.py     # 进度条显示
│       └── validators.py   # 参数验证
├── 📁 core/                   # 核心业务模块
│   ├── __init__.py
│   ├── ft1_engine.py       # FT1测试引擎
│   ├── ft2_engine.py       # FT2测试引擎
│   ├── data_manager.py     # 数据管理
│   ├── log_viewer.py       # 日志查看引擎
│   ├── device_manager.py   # 设备管理
│   └── batch_processor.py  # 批处理引擎
├── 📁 hardware/               # 硬件接口模块
│   ├── __init__.py
│   ├── nvme_interface.py   # NVMe接口
│   ├── serial_interface.py # 串口接口
│   ├── gpio_interface.py   # GPIO接口
│   └── device_detector.py  # 设备检测
├── 📁 models/                 # 数据模型
│   ├── __init__.py
│   ├── database.py         # 数据库模型
│   ├── product.py          # 产品模型
│   └── test_record.py      # 测试记录模型
├── 📁 utils/                  # 工具模块
│   ├── __init__.py
│   ├── logger.py           # 日志工具
│   ├── exceptions.py       # 异常定义
│   └── helpers.py          # 辅助函数
├── 📁 tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_ft1.py         # FT1测试用例
│   ├── test_ft2.py         # FT2测试用例
│   └── test_data.py        # 数据管理测试
├── 📁 scripts/                # 脚本和工具
│   ├── install.sh          # 安装脚本
│   ├── build.py           # 打包脚本
│   └── examples/          # 使用示例
│       ├── batch_test.yaml # 批处理配置示例
│       └── automation.sh  # 自动化脚本示例
├── 📁 doc/                   # 项目文档
│   ├── README.md          # 文档索引
│   ├── SSD_Manufacturing_System_Architecture_Overview.md
│   └── SSD_Manufacturing_Tool_Iterative_Development_Plan.md
└── 📁 data/                   # 数据目录（运行时创建）
    ├── database/           # 数据库文件
    ├── logs/              # 日志文件
    └── config/            # 用户配置文件
```

## 8. 项目实施计划

### 8.1 开发阶段
1. **Phase 1: CLI基础框架搭建（1周）**
   - 项目结构创建
   - Click命令行框架搭建
   - 基础命令和参数解析
   - 配置文件管理系统

2. **Phase 2: 核心业务模块开发（2周）**
   - 数据库模型设计
   - 硬件接口抽象层
   - FT1/FT2测试引擎基础框架
   - 设备管理模块

3. **Phase 3: FT1命令开发（2周）**
   - 固件烧写功能实现
   - ACTIVITY管脚监控
   - 串口通信模块
   - FT1相关CLI命令

4. **Phase 4: FT2命令开发（2周）**
   - PyNvme集成开发
   - NVMe设备管理
   - 测试流程控制
   - FT2相关CLI命令

5. **Phase 5: 日志和批处理功能（1周）**
   - 日志查看和导出命令
   - 批处理引擎开发
   - 自动化脚本支持
   - 输出格式化器

6. **Phase 6: 集成测试和优化（2周）**
   - 系统集成测试
   - 性能优化
   - 错误处理完善
   - 文档和示例编写

### 8.2 交付物
- 完整CLI工具（可执行文件和Python包）
- 源代码和开发文档
- 命令行用户手册和API文档
- 部署和安装指南（包含systemd服务配置）
- 批处理配置示例和自动化脚本
- 测试报告和验证文档

## 9. 风险评估和缓解

### 9.1 技术风险
- **PyNvme兼容性风险**: 不同版本SSD的兼容性问题
- **硬件接口稳定性**: 串口和GPIO通信可靠性
- **多线程并发风险**: 进程阻塞和数据竞争
- **数据安全风险**: 测试数据丢失或损坏
- **CLI兼容性风险**: 不同终端环境的兼容性

### 9.2 缓解措施
- **兼容性测试**: 建立完整的硬件测试矩阵
- **硬件抽象**: 设计统一的硬件接口层
- **线程安全**: 使用Qt线程安全机制和锁机制
- **数据备份**: 实现自动备份和恢复机制

### 9.3 项目风险
- **需求变更风险**: 制造流程调整导致需求变化
- **硬件依赖风险**: 特定硬件设备的依赖性
- **人员风险**: 关键开发人员变动

### 9.4 应对策略
- **模块化设计**: 降低需求变更影响范围
- **硬件抽象**: 减少对特定硬件的依赖
- **知识文档**: 完善的技术文档和代码注释

---

**文档版本**: v3.0 (CLI工具版)
**创建日期**: 2025-07-30
**更新日期**: 2025-07-30
**审核状态**: 待审核
