# 企业级SSD量产桌面软件系统 - 概要设计文档

## 1. 系统概述

### 1.1 项目背景
本系统是一个企业级SSD量产桌面应用软件。支持FT1（硬件基础测试）和FT2（完整功能测试）两个关键测试阶段，实现从硬件验证到最终产品交付的全流程自动化管理。

### 1.2 核心目标
- 提供直观的桌面GUI界面，支持实时操作和监控
- 自动化管理SSD生产测试全流程
- 本地化数据存储，确保测试数据安全可靠
- 支持多设备并发测试和实时状态监控
- 提供完整的日志查看和历史查询功能

## 2. 系统架构总览

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "桌面GUI界面层"
        A1[主控制面板]
        A2[测试监控界面]
        A3[数据查询界面]
        A4[设置界面]
    end

    subgraph "应用控制层"
        B1[主应用控制器]
        B2[事件管理器]
        B3[状态管理器]
        B4[配置管理]
    end

    subgraph "核心业务层"
        C1[FT1测试引擎]
        C2[FT2测试引擎]
        C3[数据管理引擎]
    end

    subgraph "硬件接口层"
        D1[PyNvme/SPDK接口]
        D2[串口通信模块]
        D3[GPIO控制模块]
        D4[设备管理模块]
    end

    subgraph "数据存储层"
        E1[SQLite本地数据库]
        E2[配置文件存储]
        E3[日志文件存储]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4

    B1 --> C1
    B2 --> C2
    B3 --> C3

    C1 --> D2
    C1 --> D3
    C2 --> D1
    C3 --> E1

    D1 --> E1
    D2 --> E3
    D3 --> E3
    D4 --> E1
```

### 2.2 技术栈选择

| 层级 | 技术选择 | 选择理由 |
|------|----------|----------|
| GUI框架 | PySide6 + Qt Designer | 企业级桌面应用标准，跨平台支持 |
| 核心语言 | Python 3.9+ | 与PyNvme完美集成，开发效率高 |
| 测试框架 | PyNvme + SPDK | SSD测试行业标准，成熟稳定 |
| 本地数据库 | SQLite + SQLAlchemy | 轻量级，无需额外服务，事务支持 |
| 硬件通信 | pySerial + RPi.GPIO | 串口和GPIO硬件接口标准库 |
| 数据处理 | pandas + numpy | 高效数据分析和处理 |

| 多线程 | QThread + concurrent.futures | 避免界面阻塞，支持并发测试 |

## 3. 核心模块设计

### 3.1 GUI界面模块
**主要界面组件：**
- **主控制面板**: 测试流程控制、设备状态总览、快速操作按钮
- **FT1测试界面**: 固件烧写进度、ACTIVITY管脚监控、硬件测试结果显示
- **FT2测试界面**: NVMe设备管理、测试进度监控、实时数据展示
- **数据查询界面**: 日志查看、历史查询、过滤搜索
- **系统设置界面**: 设备配置、测试参数、用户权限管理

**界面特性：**
- 实时状态更新（Qt信号/槽机制）
- 多标签页管理，支持同时监控多个测试
- 自定义主题和布局
- 操作日志实时显示

### 3.2 FT1测试引擎
**核心功能：**
- 固件烧写管理（支持两种方案）
- ACTIVITY管脚状态监控和解析
- 串口通信和日志采集
- 硬件测试结果判定
- 测试流程状态机管理

**关键类设计：**
```python
class FT1TestEngine:
    def start_firmware_flash(self, method: str, device_id: str) -> bool
    def monitor_activity_pin(self, device_id: str) -> ActivityStatus
    def parse_test_result(self, activity_pattern: str) -> TestResult
    def collect_serial_logs(self, port: str) -> List[str]
    def execute_hardware_test(self, device_id: str) -> TestReport
```

### 3.3 FT2测试引擎
**核心功能：**
- NVMe设备连接和管理
- 测试固件下载和执行
- NAND burn-in测试控制
- SLT系统级测试管理
- 产品信息写入和开卡

**关键类设计：**
```python
class FT2TestEngine:
    def connect_nvme_device(self, device_path: str) -> NVMeDevice
    def download_test_firmware(self, device: NVMeDevice, firmware_path: str) -> bool
    def execute_burnin_test(self, device: NVMeDevice, duration: int) -> BurninResult
    def run_slt_test(self, device: NVMeDevice) -> SLTResult
    def write_product_info(self, device: NVMeDevice, product_info: dict) -> bool
```

### 3.4 数据管理引擎
**功能职责：**
- 本地SQLite数据库管理
- 测试数据实时存储和查询
- 日志数据管理和分析
- 坏块表管理和分析
- 产品信息和序列号管理
- 数据备份和恢复

**数据模型：**
```python
# 产品信息表
class Product(Base):
    __tablename__ = 'products'
    id = Column(Integer, primary_key=True)
    serial_number = Column(String(50), unique=True)
    model = Column(String(50))
    capacity = Column(String(20))
    manufacture_date = Column(DateTime)
    current_stage = Column(String(10))  # FT1/FT2/COMPLETE

# 测试记录表
class TestRecord(Base):
    __tablename__ = 'test_records'
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'))
    test_stage = Column(String(10))  # FT1/FT2
    test_result = Column(String(10))  # PASS/FAIL
    test_data = Column(JSON)
    start_time = Column(DateTime)
    end_time = Column(DateTime)

# 坏块表
class BadBlockTable(Base):
    __tablename__ = 'bad_blocks'
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'))
    block_address = Column(BigInteger)
    block_type = Column(String(20))
    detected_stage = Column(String(10))
    severity = Column(String(10))

# 日志记录表
class LogRecord(Base):
    __tablename__ = 'log_records'
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'))
    log_type = Column(String(20))  # TEST/OPERATION/ERROR/HARDWARE
    log_level = Column(String(10))  # DEBUG/INFO/WARNING/ERROR
    message = Column(Text)
    device_id = Column(String(50))
    timestamp = Column(DateTime)
    details = Column(JSON)
```

### 3.5 日志查看系统
**功能职责：**
- 多类型日志查看（测试日志、操作日志、错误日志、硬件通信日志）
- 历史日志查询和检索
- 日志过滤和搜索（按时间、设备ID、日志级别、关键词）
- 实时日志监控和显示
- 日志数据导出功能

**关键类设计：**
```python
class LogViewerEngine:
    def get_logs_by_type(self, log_type: str, start_time: datetime, end_time: datetime) -> List[LogRecord]
    def search_logs(self, keyword: str, filters: dict) -> List[LogRecord]
    def filter_logs(self, device_id: str, log_level: str, time_range: tuple) -> List[LogRecord]
    def export_logs(self, logs: List[LogRecord], format: str) -> str
    def get_real_time_logs(self, callback: callable) -> None
```

## 4. 应用架构设计

### 4.1 多线程架构

```mermaid
graph TD
    A[主GUI线程<br/>UI更新] --> B[测试控制线程<br/>FT1/FT2测试执行]
    A --> C[硬件监控线程<br/>设备状态监控]
    A --> D[数据处理线程<br/>数据库操作]
    A --> E[日志记录线程<br/>日志文件写入]

    B --> F[FT1引擎]
    B --> G[FT2引擎]
    C --> H[设备状态监控]
    D --> I[SQLite数据库]
    E --> J[日志文件系统]
```

### 4.2 FT1测试流程

```mermaid
flowchart LR
    A[设备检测] --> B[固件烧写]
    B --> C[硬件测试启动]
    C --> D[ACTIVITY管脚监控]
    D --> E[结果解析]
    E --> F[数据存储]
    F --> G[状态更新]
    G --> H[流转到组装环节]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#e0f2f1
    style G fill:#f1f8e9
    style H fill:#e8eaf6
```

### 4.3 FT2测试流程

```mermaid
flowchart LR
    A[NVMe设备连接] --> B[FT固件下载]
    B --> C[NAND burn-in测试]
    C --> D[SLT系统测试]
    D --> E[产品信息写入]
    E --> F[最终验证]
    F --> G[测试完成]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#e0f2f1
    style G fill:#f1f8e9
```

### 4.4 状态管理机制
- **设备状态**: 空闲/测试中/完成/异常
- **测试状态**: 未开始/进行中/暂停/完成/失败
- **数据状态**: 实时同步到本地数据库
- **界面状态**: Qt信号/槽机制实时更新

## 5. 关键技术方案

### 5.1 多设备并发支持
- **QThread多线程**: 每个测试设备独立线程
- **设备资源池**: 统一管理串口、NVMe设备资源
- **任务队列**: 支持测试任务排队和调度
- **状态同步**: 线程安全的状态共享机制

### 5.2 实时监控技术
- **Qt信号/槽**: 跨线程数据传递
- **定时器机制**: QTimer定期状态检查
- **事件驱动**: 硬件状态变化触发界面更新
- **状态机**: 测试流程状态管理

### 5.3 数据持久化
- **SQLite事务**: 保证数据一致性
- **连接池**: 数据库连接复用
- **批量操作**: 提高数据写入效率
- **自动备份**: 定期数据备份机制

### 5.4 异常处理和恢复
- **分层异常处理**: GUI/业务/硬件层异常分离
- **自动重试**: 硬件通信失败自动重试
- **故障恢复**: 测试中断后状态恢复
- **日志记录**: 完整的操作和错误日志

### 5.5 安全性设计
- **用户权限**: 操作员/管理员权限分离
- **数据完整性**: 测试数据防篡改机制
- **操作审计**: 关键操作日志记录
- **配置保护**: 重要配置文件加密存储

## 6. 部署和运行环境

### 6.1 系统要求
- **操作系统**: Windows 10/11, Ubuntu 20.04+, CentOS 8+
- **Python版本**: Python 3.9+
- **内存要求**: 最低4GB，推荐8GB+
- **存储空间**: 最低10GB可用空间
- **硬件接口**: USB串口、PCIe NVMe插槽

### 6.2 依赖环境
```bash
# 核心依赖
PySide6>=6.5.0
PyNvme>=3.0
pySerial>=3.5
SQLAlchemy>=2.0
pandas>=1.5.0
numpy>=1.24.0


# 硬件控制依赖
RPi.GPIO>=0.7.1  # 树莓派GPIO控制
pyftdi>=0.54.0   # FTDI设备支持
```

### 6.3 安装部署
- **打包方式**: PyInstaller打包为独立可执行文件
- **配置管理**: JSON/YAML配置文件
- **数据目录**: 用户数据目录自动创建
- **日志管理**: 按日期轮转的日志文件

## 7. 项目目录结构

### 7.1 推荐项目结构

```
ssd_manufacturing_tool/
├── 📄 main.py                 # 应用程序入口
├── 📄 requirements.txt        # 依赖包列表
├── 📁 config/                 # 配置模块
│   ├── __init__.py
│   ├── settings.py         # 应用配置
│   └── database.py         # 数据库配置
├── 📁 gui/                    # 图形界面模块
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── ft1_widget.py       # FT1测试界面
│   ├── ft2_widget.py       # FT2测试界面
│   ├── data_widget.py      # 数据查询界面
│   ├── settings_widget.py  # 设置界面
│   └── resources/          # UI资源文件
├── 📁 core/                   # 核心业务模块
│   ├── __init__.py
│   ├── ft1_engine.py       # FT1测试引擎
│   ├── ft2_engine.py       # FT2测试引擎
│   ├── data_manager.py     # 数据管理
│   ├── log_viewer.py       # 日志查看引擎
│   └── device_manager.py   # 设备管理
├── 📁 hardware/               # 硬件接口模块
│   ├── __init__.py
│   ├── nvme_interface.py   # NVMe接口
│   ├── serial_interface.py # 串口接口
│   ├── gpio_interface.py   # GPIO接口
│   └── device_detector.py  # 设备检测
├── 📁 models/                 # 数据模型
│   ├── __init__.py
│   ├── database.py         # 数据库模型
│   ├── product.py          # 产品模型
│   └── test_record.py      # 测试记录模型
├── 📁 utils/                  # 工具模块
│   ├── __init__.py
│   ├── logger.py           # 日志工具
│   ├── exceptions.py       # 异常定义
│   └── helpers.py          # 辅助函数
├── 📁 tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_ft1.py         # FT1测试用例
│   ├── test_ft2.py         # FT2测试用例
│   └── test_data.py        # 数据管理测试
├── 📁 docs/                   # 文档
│   ├── user_manual.md      # 用户手册
│   ├── api_reference.md    # API参考
│   └── deployment.md       # 部署指南
└── 📁 data/                   # 数据目录
    ├── database/           # 数据库文件
    ├── logs/              # 日志文件
    └── config/            # 配置文件
```

## 8. 项目实施计划

### 8.1 开发阶段
1. **Phase 1: 基础框架搭建（2周）**
   - 项目结构创建
   - 基础GUI框架搭建
   - 数据库模型设计
   - 硬件接口抽象层

2. **Phase 2: FT1模块开发（3周）**
   - 固件烧写功能实现
   - ACTIVITY管脚监控
   - 串口通信模块
   - FT1测试界面开发

3. **Phase 3: FT2模块开发（3周）**
   - PyNvme集成开发
   - NVMe设备管理
   - 测试流程控制
   - FT2测试界面开发

4. **Phase 4: 日志管理和查看（2周）**
   - 日志查看界面开发
   - 历史日志查询功能
   - 日志过滤和搜索功能
   - 日志数据导出功能

5. **Phase 5: 集成测试和优化（2周）**
   - 系统集成测试
   - 性能优化
   - 用户界面优化
   - 文档编写

### 8.2 交付物
- 完整桌面应用程序（可执行文件）
- 源代码和开发文档
- 用户操作手册
- 部署和安装指南
- 测试报告和验证文档

## 9. 风险评估和缓解

### 9.1 技术风险
- **PyNvme兼容性风险**: 不同版本SSD的兼容性问题
- **硬件接口稳定性**: 串口和GPIO通信可靠性
- **多线程并发风险**: 界面卡顿和数据竞争
- **数据安全风险**: 测试数据丢失或损坏

### 9.2 缓解措施
- **兼容性测试**: 建立完整的硬件测试矩阵
- **硬件抽象**: 设计统一的硬件接口层
- **线程安全**: 使用Qt线程安全机制和锁机制
- **数据备份**: 实现自动备份和恢复机制

### 9.3 项目风险
- **需求变更风险**: 制造流程调整导致需求变化
- **硬件依赖风险**: 特定硬件设备的依赖性
- **人员风险**: 关键开发人员变动

### 9.4 应对策略
- **模块化设计**: 降低需求变更影响范围
- **硬件抽象**: 减少对特定硬件的依赖
- **知识文档**: 完善的技术文档和代码注释

---

**文档版本**: v2.0 (桌面应用版)
**创建日期**: 2025-01-28
**更新日期**: 2025-01-28
**审核状态**: 待审核
