# FT1和FT2测试引擎详细设计文档

## 1. 设计概述

### 1.1 模块架构

```mermaid
graph TB
    subgraph "测试引擎层"
        subgraph "FT1TestEngine (硬件基础测试引擎)"
            A1[FirmwareFlashManager<br/>固件烧写管理器]
            A2[ActivityPinMonitor<br/>ACTIVITY管脚监控器]
            A3[SerialCommunicator<br/>串口通信器]
            A4[HardwareTestController<br/>硬件测试控制器]
            A5[FT1StateMachine<br/>FT1状态机]
        end

        subgraph "FT2TestEngine (完整功能测试引擎)"
            B1[NVMeDeviceManager<br/>NVMe设备管理器]
            B2[FirmwareDownloader<br/>固件下载器]
            B3[BurninTestController<br/>Burn-in测试控制器]
            B4[SLTTestController<br/>SLT测试控制器]
            B5[ProductInfoWriter<br/>产品信息写入器]
            B6[FT2StateMachine<br/>FT2状态机]
        end
    end

    subgraph "硬件抽象层"
        C1[SerialInterface<br/>串口接口抽象]
        C2[GPIOInterface<br/>GPIO接口抽象]
        C3[NVMeInterface<br/>NVMe接口抽象]
    end

    A3 --> C1
    A2 --> C2
    B1 --> C3

    style A1 fill:#e1f5fe
    style A2 fill:#f3e5f5
    style A3 fill:#e8f5e8
    style A4 fill:#fff3e0
    style A5 fill:#fce4ec
    style B1 fill:#e0f2f1
    style B2 fill:#f1f8e9
    style B3 fill:#e8eaf6
    style B4 fill:#fff8e1
    style B5 fill:#fce4ec
    style B6 fill:#e1f5fe
```

### 1.2 核心设计原则
- **状态机驱动**: 每个测试阶段都有明确的状态转换
- **异步执行**: 支持多设备并发测试
- **硬件抽象**: 统一的硬件接口，便于扩展
- **异常恢复**: 完善的错误处理和状态恢复机制
- **可配置性**: 支持灵活的测试参数配置

## 2. FT1测试引擎详细设计

### 2.1 FT1TestEngine主类设计

```python
from enum import Enum
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
import asyncio
import logging
from abc import ABC, abstractmethod

class FT1TestState(Enum):
    """FT1测试状态枚举"""
    IDLE = "idle"
    DEVICE_DETECTION = "device_detection"
    FIRMWARE_FLASHING = "firmware_flashing"
    HARDWARE_TESTING = "hardware_testing"
    ACTIVITY_MONITORING = "activity_monitoring"
    RESULT_ANALYSIS = "result_analysis"
    COMPLETED = "completed"
    FAILED = "failed"
    ERROR = "error"

class FlashMethod(Enum):
    """固件烧写方法"""
    NOR_FLASH_PREBURN = "nor_flash_preburn"  # 方案1：SMT前预烧写
    SERIAL_DOWNLOAD = "serial_download"      # 方案2：串口下载烧写

@dataclass
class FT1TestConfig:
    """FT1测试配置"""
    device_id: str
    serial_port: str = "/dev/ttyUSB0"
    serial_baudrate: int = 115200
    activity_pin: int = 18
    flash_method: FlashMethod = FlashMethod.SERIAL_DOWNLOAD
    firmware_path: str = ""
    test_timeout: int = 300  # 5分钟超时
    activity_monitor_timeout: int = 60
    retry_count: int = 3

@dataclass
class ActivityPattern:
    """ACTIVITY管脚模式"""
    frequency: float  # 闪灯频率 (Hz)
    count: int       # 连续闪灯次数
    duration: float  # 持续时间 (秒)

@dataclass
class HardwareTestResult:
    """硬件测试结果"""
    ddr_test: bool = False
    nand_test: bool = False
    temperature_test: bool = False
    nor_flash_test: bool = False
    capacitor_test: bool = False
    overall_result: bool = False
    error_code: Optional[str] = None
    activity_pattern: Optional[ActivityPattern] = None
    serial_logs: List[str] = None

class FT1TestEngine:
    """FT1测试引擎主类"""
    
    def __init__(self, config: FT1TestConfig):
        self.config = config
        self.current_state = FT1TestState.IDLE
        self.test_result = HardwareTestResult()
        
        # 初始化子模块
        self.firmware_manager = FirmwareFlashManager(config)
        self.activity_monitor = ActivityPinMonitor(config.activity_pin)
        self.serial_comm = SerialCommunicator(config.serial_port, config.serial_baudrate)
        self.hardware_controller = HardwareTestController(self.serial_comm)
        self.state_machine = FT1StateMachine(self)
        
        # 事件回调
        self.state_changed_callback: Optional[Callable] = None
        self.progress_callback: Optional[Callable] = None
        
        self.logger = logging.getLogger(f"FT1Engine_{config.device_id}")
    
    async def start_test(self) -> HardwareTestResult:
        """启动FT1测试"""
        try:
            self.logger.info(f"Starting FT1 test for device {self.config.device_id}")
            await self.state_machine.start()
            return self.test_result
        except Exception as e:
            self.logger.error(f"FT1 test failed: {e}")
            await self._set_state(FT1TestState.ERROR)
            raise
    
    async def stop_test(self):
        """停止测试"""
        await self.state_machine.stop()
        await self._cleanup()
    
    async def _set_state(self, new_state: FT1TestState):
        """设置测试状态"""
        old_state = self.current_state
        self.current_state = new_state
        self.logger.info(f"State changed: {old_state} -> {new_state}")
        
        if self.state_changed_callback:
            await self.state_changed_callback(old_state, new_state)
    
    async def _cleanup(self):
        """清理资源"""
        await self.serial_comm.close()
        self.activity_monitor.cleanup()
```

### 2.2 固件烧写管理器

```python
class FirmwareFlashManager:
    """固件烧写管理器"""
    
    def __init__(self, config: FT1TestConfig):
        self.config = config
        self.logger = logging.getLogger("FirmwareFlashManager")
    
    async def flash_firmware(self) -> bool:
        """执行固件烧写"""
        try:
            if self.config.flash_method == FlashMethod.NOR_FLASH_PREBURN:
                return await self._flash_nor_preburn()
            elif self.config.flash_method == FlashMethod.SERIAL_DOWNLOAD:
                return await self._flash_serial_download()
            else:
                raise ValueError(f"Unsupported flash method: {self.config.flash_method}")
        except Exception as e:
            self.logger.error(f"Firmware flash failed: {e}")
            return False
    
    async def _flash_nor_preburn(self) -> bool:
        """方案1：SMT前预烧写NOR Flash"""
        self.logger.info("Using pre-burned NOR Flash method")
        # 这种方案下，固件已经预先烧写到NOR Flash
        # 只需要验证固件是否正确加载
        return await self._verify_preburned_firmware()
    
    async def _flash_serial_download(self) -> bool:
        """方案2：串口下载烧写"""
        self.logger.info("Using serial download method")
        
        # 步骤1：下载NOR Flash烧写固件
        if not await self._download_nor_flash_writer():
            return False
        
        # 步骤2：下载并烧写FT_BootLoader
        if not await self._download_ft_bootloader():
            return False
        
        return True
    
    async def _verify_preburned_firmware(self) -> bool:
        """验证预烧写固件"""
        # 实现固件验证逻辑
        await asyncio.sleep(1)  # 模拟验证过程
        return True
    
    async def _download_nor_flash_writer(self) -> bool:
        """下载NOR Flash烧写固件"""
        # 实现NOR Flash烧写固件下载
        await asyncio.sleep(2)  # 模拟下载过程
        return True
    
    async def _download_ft_bootloader(self) -> bool:
        """下载FT_BootLoader"""
        # 实现FT_BootLoader下载和烧写
        await asyncio.sleep(3)  # 模拟下载和烧写过程
        return True
```

### 2.3 ACTIVITY管脚监控器

```python
import time
import threading
from collections import deque

class ActivityPinMonitor:
    """ACTIVITY管脚监控器"""
    
    def __init__(self, pin_number: int):
        self.pin_number = pin_number
        self.is_monitoring = False
        self.monitor_thread = None
        self.pattern_data = deque(maxlen=1000)
        self.logger = logging.getLogger("ActivityPinMonitor")
        
        # 初始化GPIO
        self._init_gpio()
    
    def _init_gpio(self):
        """初始化GPIO"""
        try:
            import RPi.GPIO as GPIO
            GPIO.setmode(GPIO.BCM)
            GPIO.setup(self.pin_number, GPIO.IN, pull_up_down=GPIO.PUD_DOWN)
            self.gpio = GPIO
            self.logger.info(f"GPIO pin {self.pin_number} initialized")
        except ImportError:
            self.logger.warning("RPi.GPIO not available, using mock GPIO")
            self.gpio = self._create_mock_gpio()
    
    def _create_mock_gpio(self):
        """创建模拟GPIO（用于开发测试）"""
        class MockGPIO:
            @staticmethod
            def input(pin):
                # 模拟ACTIVITY管脚信号
                return int(time.time() * 2) % 2
        return MockGPIO()
    
    async def start_monitoring(self, timeout: int = 60) -> ActivityPattern:
        """开始监控ACTIVITY管脚"""
        self.is_monitoring = True
        self.pattern_data.clear()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(timeout,)
        )
        self.monitor_thread.start()
        
        # 等待监控完成
        self.monitor_thread.join()
        
        # 分析模式
        return self._analyze_pattern()
    
    def _monitor_loop(self, timeout: int):
        """监控循环"""
        start_time = time.time()
        sample_interval = 0.01  # 10ms采样间隔
        
        while self.is_monitoring and (time.time() - start_time) < timeout:
            pin_state = self.gpio.input(self.pin_number)
            timestamp = time.time()
            
            self.pattern_data.append((timestamp, pin_state))
            time.sleep(sample_interval)
    
    def _analyze_pattern(self) -> ActivityPattern:
        """分析ACTIVITY管脚模式"""
        if not self.pattern_data:
            return ActivityPattern(0, 0, 0)
        
        # 检测状态变化
        transitions = []
        prev_state = self.pattern_data[0][1]
        
        for timestamp, state in self.pattern_data:
            if state != prev_state:
                transitions.append((timestamp, state))
                prev_state = state
        
        if len(transitions) < 2:
            return ActivityPattern(0, 0, 0)
        
        # 计算频率
        high_periods = []
        for i in range(0, len(transitions)-1, 2):
            if i+1 < len(transitions):
                period = transitions[i+1][0] - transitions[i][0]
                high_periods.append(period)
        
        if high_periods:
            avg_period = sum(high_periods) / len(high_periods)
            frequency = 1.0 / (avg_period * 2) if avg_period > 0 else 0
        else:
            frequency = 0
        
        # 计算闪灯次数和持续时间
        count = len(high_periods)
        duration = self.pattern_data[-1][0] - self.pattern_data[0][0]
        
        pattern = ActivityPattern(frequency, count, duration)
        self.logger.info(f"Detected pattern: freq={frequency:.2f}Hz, count={count}, duration={duration:.2f}s")
        
        return pattern
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join()
    
    def cleanup(self):
        """清理资源"""
        self.stop_monitoring()
        try:
            self.gpio.cleanup()
        except:
            pass
```

### 2.4 串口通信器

```python
import serial
import asyncio
from typing import List

class SerialCommunicator:
    """串口通信器"""
    
    def __init__(self, port: str, baudrate: int):
        self.port = port
        self.baudrate = baudrate
        self.serial_conn = None
        self.is_connected = False
        self.log_buffer = deque(maxlen=1000)
        self.logger = logging.getLogger("SerialCommunicator")
    
    async def connect(self) -> bool:
        """连接串口"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                bytesize=serial.EIGHTBITS
            )
            self.is_connected = True
            self.logger.info(f"Serial connected: {self.port}@{self.baudrate}")
            return True
        except Exception as e:
            self.logger.error(f"Serial connection failed: {e}")
            return False
    
    async def send_command(self, command: str) -> bool:
        """发送命令"""
        if not self.is_connected:
            return False
        
        try:
            cmd_bytes = (command + '\r\n').encode('utf-8')
            self.serial_conn.write(cmd_bytes)
            self.logger.debug(f"Sent command: {command}")
            return True
        except Exception as e:
            self.logger.error(f"Send command failed: {e}")
            return False
    
    async def read_response(self, timeout: float = 5.0) -> str:
        """读取响应"""
        if not self.is_connected:
            return ""
        
        try:
            start_time = time.time()
            response = ""
            
            while (time.time() - start_time) < timeout:
                if self.serial_conn.in_waiting > 0:
                    data = self.serial_conn.read(self.serial_conn.in_waiting)
                    response += data.decode('utf-8', errors='ignore')
                    
                    if '\n' in response:
                        break
                
                await asyncio.sleep(0.01)
            
            # 记录到日志缓冲区
            if response.strip():
                self.log_buffer.append((time.time(), response.strip()))
                self.logger.debug(f"Received: {response.strip()}")
            
            return response.strip()
        except Exception as e:
            self.logger.error(f"Read response failed: {e}")
            return ""
    
    def get_logs(self, since: float = 0) -> List[str]:
        """获取日志"""
        return [log for timestamp, log in self.log_buffer if timestamp >= since]
    
    async def close(self):
        """关闭连接"""
        if self.serial_conn and self.is_connected:
            self.serial_conn.close()
            self.is_connected = False
            self.logger.info("Serial connection closed")
```

### 2.5 硬件测试控制器

```python
class HardwareTestController:
    """硬件测试控制器"""
    
    def __init__(self, serial_comm: SerialCommunicator):
        self.serial_comm = serial_comm
        self.logger = logging.getLogger("HardwareTestController")
    
    async def run_hardware_tests(self) -> HardwareTestResult:
        """运行硬件测试"""
        result = HardwareTestResult()
        
        try:
            # 等待FT_BootLoader启动
            await self._wait_for_bootloader()
            
            # 执行各项硬件测试
            result.ddr_test = await self._test_ddr()
            result.nand_test = await self._test_nand()
            result.temperature_test = await self._test_temperature()
            result.nor_flash_test = await self._test_nor_flash()
            result.capacitor_test = await self._test_capacitor()
            
            # 判断总体结果
            result.overall_result = all([
                result.ddr_test,
                result.nand_test,
                result.temperature_test,
                result.nor_flash_test,
                result.capacitor_test
            ])
            
            # 收集串口日志
            result.serial_logs = self.serial_comm.get_logs()
            
            self.logger.info(f"Hardware test completed: {result.overall_result}")
            return result
            
        except Exception as e:
            self.logger.error(f"Hardware test failed: {e}")
            result.error_code = str(e)
            return result
    
    async def _wait_for_bootloader(self, timeout: int = 30):
        """等待FT_BootLoader启动"""
        start_time = time.time()
        
        while (time.time() - start_time) < timeout:
            response = await self.serial_comm.read_response(1.0)
            if "FT_BootLoader" in response or "Ready" in response:
                self.logger.info("FT_BootLoader detected")
                return
            await asyncio.sleep(0.5)
        
        raise TimeoutError("FT_BootLoader not detected")
    
    async def _test_ddr(self) -> bool:
        """测试DDR内存"""
        await self.serial_comm.send_command("test_ddr")
        response = await self.serial_comm.read_response(10.0)
        return "DDR_OK" in response
    
    async def _test_nand(self) -> bool:
        """测试NAND闪存"""
        await self.serial_comm.send_command("test_nand")
        response = await self.serial_comm.read_response(15.0)
        return "NAND_OK" in response
    
    async def _test_temperature(self) -> bool:
        """测试温度传感器"""
        await self.serial_comm.send_command("test_temp")
        response = await self.serial_comm.read_response(5.0)
        return "TEMP_OK" in response
    
    async def _test_nor_flash(self) -> bool:
        """测试NOR Flash"""
        await self.serial_comm.send_command("test_nor")
        response = await self.serial_comm.read_response(10.0)
        return "NOR_OK" in response
    
    async def _test_capacitor(self) -> bool:
        """测试电容"""
        await self.serial_comm.send_command("test_cap")
        response = await self.serial_comm.read_response(5.0)
        return "CAP_OK" in response

### 2.6 FT1状态机

```python
class FT1StateMachine:
    """FT1测试状态机"""

    def __init__(self, engine: FT1TestEngine):
        self.engine = engine
        self.is_running = False
        self.logger = logging.getLogger("FT1StateMachine")

    async def start(self):
        """启动状态机"""
        self.is_running = True
        await self._execute_state_flow()

    async def stop(self):
        """停止状态机"""
        self.is_running = False

    async def _execute_state_flow(self):
        """执行状态流程"""
        try:
            # 设备检测
            await self.engine._set_state(FT1TestState.DEVICE_DETECTION)
            if not await self._device_detection():
                await self.engine._set_state(FT1TestState.FAILED)
                return

            # 固件烧写
            await self.engine._set_state(FT1TestState.FIRMWARE_FLASHING)
            if not await self._firmware_flashing():
                await self.engine._set_state(FT1TestState.FAILED)
                return

            # 硬件测试
            await self.engine._set_state(FT1TestState.HARDWARE_TESTING)
            if not await self._hardware_testing():
                await self.engine._set_state(FT1TestState.FAILED)
                return

            # ACTIVITY监控
            await self.engine._set_state(FT1TestState.ACTIVITY_MONITORING)
            if not await self._activity_monitoring():
                await self.engine._set_state(FT1TestState.FAILED)
                return

            # 结果分析
            await self.engine._set_state(FT1TestState.RESULT_ANALYSIS)
            if not await self._result_analysis():
                await self.engine._set_state(FT1TestState.FAILED)
                return

            # 测试完成
            await self.engine._set_state(FT1TestState.COMPLETED)

        except Exception as e:
            self.logger.error(f"State machine error: {e}")
            await self.engine._set_state(FT1TestState.ERROR)

    async def _device_detection(self) -> bool:
        """设备检测"""
        return await self.engine.serial_comm.connect()

    async def _firmware_flashing(self) -> bool:
        """固件烧写"""
        return await self.engine.firmware_manager.flash_firmware()

    async def _hardware_testing(self) -> bool:
        """硬件测试"""
        result = await self.engine.hardware_controller.run_hardware_tests()
        self.engine.test_result = result
        return result.overall_result

    async def _activity_monitoring(self) -> bool:
        """ACTIVITY监控"""
        pattern = await self.engine.activity_monitor.start_monitoring(
            self.engine.config.activity_monitor_timeout
        )
        self.engine.test_result.activity_pattern = pattern
        return True

    async def _result_analysis(self) -> bool:
        """结果分析"""
        # 根据ACTIVITY模式分析最终结果
        pattern = self.engine.test_result.activity_pattern
        if pattern and pattern.frequency > 0:
            # 根据闪灯模式判断具体硬件问题
            error_code = self._decode_activity_pattern(pattern)
            if error_code:
                self.engine.test_result.error_code = error_code
                self.engine.test_result.overall_result = False

        return self.engine.test_result.overall_result

    def _decode_activity_pattern(self, pattern: ActivityPattern) -> Optional[str]:
        """解码ACTIVITY模式"""
        # 根据闪灯频率和次数判断硬件问题
        if 1.0 <= pattern.frequency <= 2.0:
            if pattern.count <= 3:
                return "DDR_ERROR"
            elif pattern.count <= 6:
                return "NAND_ERROR"
            elif pattern.count <= 9:
                return "TEMP_ERROR"
        elif 2.0 < pattern.frequency <= 4.0:
            if pattern.count <= 3:
                return "NOR_ERROR"
            elif pattern.count <= 6:
                return "CAP_ERROR"

        return None

## 3. FT2测试引擎详细设计

### 3.1 FT2TestEngine主类设计

```python
class FT2TestState(Enum):
    """FT2测试状态枚举"""
    IDLE = "idle"
    NVME_CONNECTING = "nvme_connecting"
    FT_FIRMWARE_DOWNLOAD = "ft_firmware_download"
    BURNIN_TESTING = "burnin_testing"
    SLT_TESTING = "slt_testing"
    PRODUCT_INFO_WRITING = "product_info_writing"
    FINAL_VERIFICATION = "final_verification"
    COMPLETED = "completed"
    FAILED = "failed"
    ERROR = "error"

@dataclass
class FT2TestConfig:
    """FT2测试配置"""
    device_id: str
    nvme_device_path: str = ""  # 如 "0000:03:00.0"
    ft_firmware_path: str = ""
    user_firmware_path: str = ""
    burnin_duration: int = 3600  # burn-in时长(秒)
    slt_test_suite: str = "standard"
    product_info: Dict = None
    test_timeout: int = 7200  # 2小时超时
    retry_count: int = 3

@dataclass
class BurninTestResult:
    """Burn-in测试结果"""
    duration: int = 0
    io_count: int = 0
    error_count: int = 0
    bad_blocks: List[int] = None
    performance_data: Dict = None
    success: bool = False

@dataclass
class SLTTestResult:
    """SLT测试结果"""
    test_cases_passed: int = 0
    test_cases_failed: int = 0
    test_details: Dict = None
    performance_metrics: Dict = None
    success: bool = False

@dataclass
class FT2TestResult:
    """FT2测试结果"""
    nvme_connection: bool = False
    ft_firmware_download: bool = False
    burnin_result: BurninTestResult = None
    slt_result: SLTTestResult = None
    product_info_written: bool = False
    final_verification: bool = False
    overall_result: bool = False
    error_code: Optional[str] = None

class FT2TestEngine:
    """FT2测试引擎主类"""

    def __init__(self, config: FT2TestConfig):
        self.config = config
        self.current_state = FT2TestState.IDLE
        self.test_result = FT2TestResult()

        # 初始化子模块
        self.nvme_manager = NVMeDeviceManager(config)
        self.firmware_downloader = FirmwareDownloader(config)
        self.burnin_controller = BurninTestController(config)
        self.slt_controller = SLTTestController(config)
        self.product_writer = ProductInfoWriter(config)
        self.state_machine = FT2StateMachine(self)

        # 事件回调
        self.state_changed_callback: Optional[Callable] = None
        self.progress_callback: Optional[Callable] = None

        self.logger = logging.getLogger(f"FT2Engine_{config.device_id}")

    async def start_test(self) -> FT2TestResult:
        """启动FT2测试"""
        try:
            self.logger.info(f"Starting FT2 test for device {self.config.device_id}")
            await self.state_machine.start()
            return self.test_result
        except Exception as e:
            self.logger.error(f"FT2 test failed: {e}")
            await self._set_state(FT2TestState.ERROR)
            raise

    async def stop_test(self):
        """停止测试"""
        await self.state_machine.stop()
        await self._cleanup()

    async def _set_state(self, new_state: FT2TestState):
        """设置测试状态"""
        old_state = self.current_state
        self.current_state = new_state
        self.logger.info(f"State changed: {old_state} -> {new_state}")

        if self.state_changed_callback:
            await self.state_changed_callback(old_state, new_state)

    async def _cleanup(self):
        """清理资源"""
        await self.nvme_manager.disconnect()
```

### 3.2 NVMe设备管理器

```python
from nvme import Controller, Namespace, Pcie

class NVMeDeviceManager:
    """NVMe设备管理器"""

    def __init__(self, config: FT2TestConfig):
        self.config = config
        self.pcie = None
        self.controller = None
        self.namespace = None
        self.is_connected = False
        self.logger = logging.getLogger("NVMeDeviceManager")

    async def connect_device(self) -> bool:
        """连接NVMe设备"""
        try:
            # 初始化PCIe接口
            self.pcie = Pcie(self.config.nvme_device_path)

            # 初始化NVMe控制器
            self.controller = Controller(self.pcie)

            # 获取命名空间
            self.namespace = Namespace(self.controller, 1)

            self.is_connected = True
            self.logger.info(f"NVMe device connected: {self.config.nvme_device_path}")

            # 获取设备信息
            model = self.controller.id_data(63, 24, str)
            serial = self.controller.id_data(23, 4, str)
            self.logger.info(f"Device info: Model={model}, Serial={serial}")

            return True

        except Exception as e:
            self.logger.error(f"NVMe connection failed: {e}")
            return False

    async def disconnect(self):
        """断开连接"""
        if self.is_connected:
            try:
                if self.namespace:
                    self.namespace.close()
                if self.controller:
                    self.controller.close()
                if self.pcie:
                    self.pcie.close()
                self.is_connected = False
                self.logger.info("NVMe device disconnected")
            except Exception as e:
                self.logger.error(f"Disconnect error: {e}")

    def get_device_info(self) -> Dict:
        """获取设备信息"""
        if not self.is_connected:
            return {}

        try:
            return {
                "model": self.controller.id_data(63, 24, str),
                "serial": self.controller.id_data(23, 4, str),
                "firmware": self.controller.id_data(71, 64, str),
                "capacity": self.namespace.id_data(7, 0),
                "lba_format": self.namespace.id_data(26, 26) & 0xf
            }
        except Exception as e:
            self.logger.error(f"Get device info failed: {e}")
            return {}

### 3.3 固件下载器

```python
class FirmwareDownloader:
    """固件下载器"""

    def __init__(self, config: FT2TestConfig):
        self.config = config
        self.logger = logging.getLogger("FirmwareDownloader")

    async def download_ft_firmware(self, controller: Controller) -> bool:
        """下载FT测试固件"""
        try:
            self.logger.info("Downloading FT firmware")

            # 读取固件文件
            firmware_data = await self._read_firmware_file(self.config.ft_firmware_path)
            if not firmware_data:
                return False

            # 执行固件下载
            success = await self._execute_firmware_download(controller, firmware_data)

            if success:
                self.logger.info("FT firmware download completed")
            else:
                self.logger.error("FT firmware download failed")

            return success

        except Exception as e:
            self.logger.error(f"FT firmware download error: {e}")
            return False

    async def download_user_firmware(self, controller: Controller) -> bool:
        """下载用户固件"""
        try:
            self.logger.info("Downloading User firmware")

            # 读取固件文件
            firmware_data = await self._read_firmware_file(self.config.user_firmware_path)
            if not firmware_data:
                return False

            # 执行固件下载
            success = await self._execute_firmware_download(controller, firmware_data)

            if success:
                self.logger.info("User firmware download completed")
            else:
                self.logger.error("User firmware download failed")

            return success

        except Exception as e:
            self.logger.error(f"User firmware download error: {e}")
            return False

    async def _read_firmware_file(self, firmware_path: str) -> Optional[bytes]:
        """读取固件文件"""
        try:
            with open(firmware_path, 'rb') as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"Read firmware file failed: {e}")
            return None

    async def _execute_firmware_download(self, controller: Controller, firmware_data: bytes) -> bool:
        """执行固件下载"""
        try:
            # 分块下载固件
            chunk_size = 4096
            offset = 0

            while offset < len(firmware_data):
                chunk = firmware_data[offset:offset + chunk_size]

                # 使用PyNvme的固件下载命令
                controller.fw_download(chunk, offset).waitdone()

                offset += len(chunk)

                # 更新进度
                progress = (offset / len(firmware_data)) * 100
                self.logger.debug(f"Download progress: {progress:.1f}%")

            # 提交固件
            controller.fw_commit(0, 0).waitdone()

            # 等待固件激活
            await asyncio.sleep(5)

            return True

        except Exception as e:
            self.logger.error(f"Firmware download execution failed: {e}")
            return False

### 3.4 Burn-in测试控制器

```python
from nvme import Buffer, IOWorker

class BurninTestController:
    """Burn-in测试控制器"""

    def __init__(self, config: FT2TestConfig):
        self.config = config
        self.logger = logging.getLogger("BurninTestController")

    async def run_burnin_test(self, namespace: Namespace) -> BurninTestResult:
        """运行burn-in测试"""
        result = BurninTestResult()

        try:
            self.logger.info(f"Starting burn-in test for {self.config.burnin_duration} seconds")

            # 获取命名空间信息
            ns_size = namespace.id_data(7, 0)  # 命名空间大小(LBA)
            lba_size = 1 << ((namespace.id_data(26, 26) & 0xf) + 9)  # LBA大小

            self.logger.info(f"Namespace size: {ns_size} LBAs, LBA size: {lba_size} bytes")

            # 创建IOWorker进行burn-in测试
            with namespace.ioworker(
                io_size=8,  # 4KB IO
                lba_align=8,
                lba_random=True,
                read_percentage=30,  # 30%读，70%写
                time=self.config.burnin_duration,
                region_end=ns_size
            ) as worker:

                # 监控测试进度
                start_time = time.time()
                while worker.running:
                    await asyncio.sleep(10)

                    elapsed = time.time() - start_time
                    progress = (elapsed / self.config.burnin_duration) * 100

                    self.logger.info(f"Burn-in progress: {progress:.1f}%")

                # 获取测试结果
                worker_result = worker.close()

                result.duration = int(elapsed)
                result.io_count = worker_result.io_count_read + worker_result.io_count_nonread
                result.error_count = worker_result.error_count
                result.success = (worker_result.error_count == 0)

                # 收集性能数据
                result.performance_data = {
                    "iops": result.io_count / elapsed,
                    "bandwidth_mbps": (result.io_count * 4096) / (elapsed * 1024 * 1024),
                    "cpu_usage": worker_result.cpu_usage
                }

                self.logger.info(f"Burn-in completed: {result.io_count} IOs, {result.error_count} errors")

            # 扫描坏块
            result.bad_blocks = await self._scan_bad_blocks(namespace)

            return result

        except Exception as e:
            self.logger.error(f"Burn-in test failed: {e}")
            result.success = False
            return result

    async def _scan_bad_blocks(self, namespace: Namespace) -> List[int]:
        """扫描坏块"""
        bad_blocks = []

        try:
            # 这里应该实现坏块扫描逻辑
            # 可以通过读取特定的日志页面或执行特定的命令来获取坏块信息
            self.logger.info("Scanning bad blocks...")

            # 模拟坏块扫描
            await asyncio.sleep(1)

            self.logger.info(f"Found {len(bad_blocks)} bad blocks")
            return bad_blocks

        except Exception as e:
            self.logger.error(f"Bad block scan failed: {e}")
            return []

### 3.5 SLT测试控制器

```python
class SLTTestController:
    """SLT系统级测试控制器"""

    def __init__(self, config: FT2TestConfig):
        self.config = config
        self.logger = logging.getLogger("SLTTestController")

    async def run_slt_test(self, controller: Controller, namespace: Namespace) -> SLTTestResult:
        """运行SLT测试"""
        result = SLTTestResult()

        try:
            self.logger.info("Starting SLT test")

            # 执行测试套件
            if self.config.slt_test_suite == "standard":
                await self._run_standard_slt(controller, namespace, result)
            elif self.config.slt_test_suite == "extended":
                await self._run_extended_slt(controller, namespace, result)
            else:
                raise ValueError(f"Unknown SLT test suite: {self.config.slt_test_suite}")

            # 判断总体结果
            result.success = (result.test_cases_failed == 0)

            self.logger.info(f"SLT completed: {result.test_cases_passed} passed, {result.test_cases_failed} failed")
            return result

        except Exception as e:
            self.logger.error(f"SLT test failed: {e}")
            result.success = False
            return result

    async def _run_standard_slt(self, controller: Controller, namespace: Namespace, result: SLTTestResult):
        """运行标准SLT测试"""
        test_cases = [
            ("format_test", self._test_format),
            ("basic_io_test", self._test_basic_io),
            ("performance_test", self._test_performance),
            ("power_cycle_test", self._test_power_cycle),
            ("error_handling_test", self._test_error_handling)
        ]

        result.test_details = {}

        for test_name, test_func in test_cases:
            try:
                self.logger.info(f"Running {test_name}")
                test_result = await test_func(controller, namespace)

                result.test_details[test_name] = test_result

                if test_result.get("success", False):
                    result.test_cases_passed += 1
                else:
                    result.test_cases_failed += 1

            except Exception as e:
                self.logger.error(f"{test_name} failed: {e}")
                result.test_cases_failed += 1
                result.test_details[test_name] = {"success": False, "error": str(e)}

    async def _test_format(self, controller: Controller, namespace: Namespace) -> Dict:
        """格式化测试"""
        try:
            namespace.format()
            return {"success": True, "message": "Format completed"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_basic_io(self, controller: Controller, namespace: Namespace) -> Dict:
        """基础IO测试"""
        try:
            # 创建测试缓冲区
            write_buf = Buffer(4096)
            read_buf = Buffer(4096)

            # 填充测试数据
            write_buf[:] = b'A' * 4096

            # 执行写入和读取
            with namespace.ioworker(io_size=8, lba_count=1000, read_percentage=50) as worker:
                pass

            return {"success": True, "message": "Basic IO test passed"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_performance(self, controller: Controller, namespace: Namespace) -> Dict:
        """性能测试"""
        try:
            # 4K随机读写性能测试
            with namespace.ioworker(
                io_size=8,
                lba_random=True,
                read_percentage=50,
                time=60
            ) as worker:
                pass

            worker_result = worker.close()
            iops = (worker_result.io_count_read + worker_result.io_count_nonread) / 60

            return {
                "success": True,
                "iops": iops,
                "message": f"Performance test completed: {iops:.0f} IOPS"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_power_cycle(self, controller: Controller, namespace: Namespace) -> Dict:
        """电源循环测试"""
        try:
            # 模拟电源循环
            controller.reset()
            await asyncio.sleep(2)

            return {"success": True, "message": "Power cycle test passed"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_error_handling(self, controller: Controller, namespace: Namespace) -> Dict:
        """错误处理测试"""
        try:
            # 测试错误处理能力
            # 这里可以注入一些错误条件来测试设备的错误处理

            return {"success": True, "message": "Error handling test passed"}
        except Exception as e:
            return {"success": False, "error": str(e)}

### 3.6 产品信息写入器

```python
class ProductInfoWriter:
    """产品信息写入器"""

    def __init__(self, config: FT2TestConfig):
        self.config = config
        self.logger = logging.getLogger("ProductInfoWriter")

    async def write_product_info(self, controller: Controller, namespace: Namespace) -> bool:
        """写入产品信息"""
        try:
            if not self.config.product_info:
                self.logger.warning("No product info to write")
                return True

            self.logger.info("Writing product information")

            # 重新开卡（格式化）
            await self._reformat_device(namespace)

            # 写入序列号
            if "serial_number" in self.config.product_info:
                await self._write_serial_number(controller, self.config.product_info["serial_number"])

            # 写入型号信息
            if "model_name" in self.config.product_info:
                await self._write_model_name(controller, self.config.product_info["model_name"])

            # 写入容量信息
            if "capacity" in self.config.product_info:
                await self._write_capacity_info(controller, self.config.product_info["capacity"])

            # 写入制造日期
            if "manufacture_date" in self.config.product_info:
                await self._write_manufacture_date(controller, self.config.product_info["manufacture_date"])

            # 写入其他自定义信息
            await self._write_custom_info(controller, self.config.product_info)

            self.logger.info("Product information written successfully")
            return True

        except Exception as e:
            self.logger.error(f"Write product info failed: {e}")
            return False

    async def _reformat_device(self, namespace: Namespace):
        """重新格式化设备"""
        try:
            self.logger.info("Reformatting device")
            namespace.format()
            await asyncio.sleep(2)  # 等待格式化完成
        except Exception as e:
            self.logger.error(f"Reformat failed: {e}")
            raise

    async def _write_serial_number(self, controller: Controller, serial_number: str):
        """写入序列号"""
        try:
            # 使用NVMe的Set Features命令写入序列号
            # 这里需要根据具体的固件实现来调整
            self.logger.info(f"Writing serial number: {serial_number}")

            # 示例：使用vendor specific feature
            feature_data = serial_number.encode('utf-8').ljust(20, b'\x00')
            controller.setfeatures(0x80, cdw11=0, data=feature_data).waitdone()

        except Exception as e:
            self.logger.error(f"Write serial number failed: {e}")
            raise

    async def _write_model_name(self, controller: Controller, model_name: str):
        """写入型号名称"""
        try:
            self.logger.info(f"Writing model name: {model_name}")

            # 示例：使用vendor specific feature
            feature_data = model_name.encode('utf-8').ljust(40, b'\x00')
            controller.setfeatures(0x81, cdw11=0, data=feature_data).waitdone()

        except Exception as e:
            self.logger.error(f"Write model name failed: {e}")
            raise

    async def _write_capacity_info(self, controller: Controller, capacity: str):
        """写入容量信息"""
        try:
            self.logger.info(f"Writing capacity info: {capacity}")

            # 示例：使用vendor specific feature
            feature_data = capacity.encode('utf-8').ljust(16, b'\x00')
            controller.setfeatures(0x82, cdw11=0, data=feature_data).waitdone()

        except Exception as e:
            self.logger.error(f"Write capacity info failed: {e}")
            raise

    async def _write_manufacture_date(self, controller: Controller, manufacture_date: str):
        """写入制造日期"""
        try:
            self.logger.info(f"Writing manufacture date: {manufacture_date}")

            # 示例：使用vendor specific feature
            feature_data = manufacture_date.encode('utf-8').ljust(16, b'\x00')
            controller.setfeatures(0x83, cdw11=0, data=feature_data).waitdone()

        except Exception as e:
            self.logger.error(f"Write manufacture date failed: {e}")
            raise

    async def _write_custom_info(self, controller: Controller, product_info: Dict):
        """写入自定义信息"""
        try:
            # 写入其他自定义字段
            custom_fields = {k: v for k, v in product_info.items()
                           if k not in ["serial_number", "model_name", "capacity", "manufacture_date"]}

            if custom_fields:
                self.logger.info(f"Writing custom info: {custom_fields}")

                # 将自定义信息序列化并写入
                import json
                custom_data = json.dumps(custom_fields).encode('utf-8')

                if len(custom_data) <= 512:  # 限制大小
                    padded_data = custom_data.ljust(512, b'\x00')
                    controller.setfeatures(0x84, cdw11=0, data=padded_data).waitdone()

        except Exception as e:
            self.logger.error(f"Write custom info failed: {e}")
            # 自定义信息写入失败不抛出异常

### 3.7 FT2状态机

```python
class FT2StateMachine:
    """FT2测试状态机"""

    def __init__(self, engine: FT2TestEngine):
        self.engine = engine
        self.is_running = False
        self.logger = logging.getLogger("FT2StateMachine")

    async def start(self):
        """启动状态机"""
        self.is_running = True
        await self._execute_state_flow()

    async def stop(self):
        """停止状态机"""
        self.is_running = False

    async def _execute_state_flow(self):
        """执行状态流程"""
        try:
            # NVMe连接
            await self.engine._set_state(FT2TestState.NVME_CONNECTING)
            if not await self._nvme_connecting():
                await self.engine._set_state(FT2TestState.FAILED)
                return

            # FT固件下载
            await self.engine._set_state(FT2TestState.FT_FIRMWARE_DOWNLOAD)
            if not await self._ft_firmware_download():
                await self.engine._set_state(FT2TestState.FAILED)
                return

            # Burn-in测试
            await self.engine._set_state(FT2TestState.BURNIN_TESTING)
            if not await self._burnin_testing():
                await self.engine._set_state(FT2TestState.FAILED)
                return

            # SLT测试
            await self.engine._set_state(FT2TestState.SLT_TESTING)
            if not await self._slt_testing():
                await self.engine._set_state(FT2TestState.FAILED)
                return

            # 产品信息写入
            await self.engine._set_state(FT2TestState.PRODUCT_INFO_WRITING)
            if not await self._product_info_writing():
                await self.engine._set_state(FT2TestState.FAILED)
                return

            # 最终验证
            await self.engine._set_state(FT2TestState.FINAL_VERIFICATION)
            if not await self._final_verification():
                await self.engine._set_state(FT2TestState.FAILED)
                return

            # 测试完成
            await self.engine._set_state(FT2TestState.COMPLETED)

        except Exception as e:
            self.logger.error(f"State machine error: {e}")
            await self.engine._set_state(FT2TestState.ERROR)

    async def _nvme_connecting(self) -> bool:
        """NVMe连接"""
        success = await self.engine.nvme_manager.connect_device()
        self.engine.test_result.nvme_connection = success
        return success

    async def _ft_firmware_download(self) -> bool:
        """FT固件下载"""
        success = await self.engine.firmware_downloader.download_ft_firmware(
            self.engine.nvme_manager.controller
        )
        self.engine.test_result.ft_firmware_download = success
        return success

    async def _burnin_testing(self) -> bool:
        """Burn-in测试"""
        result = await self.engine.burnin_controller.run_burnin_test(
            self.engine.nvme_manager.namespace
        )
        self.engine.test_result.burnin_result = result
        return result.success

    async def _slt_testing(self) -> bool:
        """SLT测试"""
        # 首先下载用户固件
        firmware_success = await self.engine.firmware_downloader.download_user_firmware(
            self.engine.nvme_manager.controller
        )
        if not firmware_success:
            return False

        # 执行SLT测试
        result = await self.engine.slt_controller.run_slt_test(
            self.engine.nvme_manager.controller,
            self.engine.nvme_manager.namespace
        )
        self.engine.test_result.slt_result = result
        return result.success

    async def _product_info_writing(self) -> bool:
        """产品信息写入"""
        success = await self.engine.product_writer.write_product_info(
            self.engine.nvme_manager.controller,
            self.engine.nvme_manager.namespace
        )
        self.engine.test_result.product_info_written = success
        return success

    async def _final_verification(self) -> bool:
        """最终验证"""
        try:
            # 验证设备信息
            device_info = self.engine.nvme_manager.get_device_info()

            # 验证产品信息是否正确写入
            if self.engine.config.product_info:
                expected_serial = self.engine.config.product_info.get("serial_number")
                if expected_serial and device_info.get("serial") != expected_serial:
                    self.logger.error("Serial number verification failed")
                    return False

            # 执行最终功能验证
            namespace = self.engine.nvme_manager.namespace
            with namespace.ioworker(io_size=8, lba_count=100, read_percentage=100) as worker:
                pass

            worker_result = worker.close()
            if worker_result.error_count > 0:
                self.logger.error("Final verification IO test failed")
                return False

            self.engine.test_result.final_verification = True
            self.engine.test_result.overall_result = True

            self.logger.info("Final verification passed")
            return True

        except Exception as e:
            self.logger.error(f"Final verification failed: {e}")
            return False

## 4. 异常处理和恢复机制

### 4.1 异常分类

```python
class TestEngineException(Exception):
    """测试引擎基础异常"""
    pass

class HardwareException(TestEngineException):
    """硬件相关异常"""
    pass

class FirmwareException(TestEngineException):
    """固件相关异常"""
    pass

class CommunicationException(TestEngineException):
    """通信相关异常"""
    pass

class TimeoutException(TestEngineException):
    """超时异常"""
    pass

class ConfigurationException(TestEngineException):
    """配置异常"""
    pass
```

### 4.2 重试机制

```python
import functools

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, exceptions: tuple = (Exception,)):
    """重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        logging.warning(f"Attempt {attempt + 1} failed: {e}, retrying in {delay}s")
                        await asyncio.sleep(delay)
                    else:
                        logging.error(f"All {max_retries + 1} attempts failed")
                        raise last_exception

            raise last_exception
        return wrapper
    return decorator
```

## 5. 配置管理

### 5.1 配置文件结构

```yaml
# config/test_config.yaml
ft1_config:
  default_serial_port: "/dev/ttyUSB0"
  default_baudrate: 115200
  default_activity_pin: 18
  default_flash_method: "serial_download"
  default_timeout: 300
  firmware_paths:
    ft_bootloader: "/firmware/ft_bootloader.bin"
    nor_flash_writer: "/firmware/nor_flash_writer.bin"

ft2_config:
  default_burnin_duration: 3600
  default_slt_suite: "standard"
  default_timeout: 7200
  firmware_paths:
    ft_firmware: "/firmware/ft_test.bin"
    user_firmware: "/firmware/user.bin"

hardware_config:
  gpio_pins:
    activity_pin: 18
    power_control_pin: 19
  serial_ports:
    - "/dev/ttyUSB0"
    - "/dev/ttyUSB1"
  nvme_devices:
    - "0000:03:00.0"
    - "0000:04:00.0"

logging_config:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "/logs/test_engine.log"
  max_file_size: "10MB"
  backup_count: 5
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-28
**模块状态**: 详细设计完成
```
```
```
