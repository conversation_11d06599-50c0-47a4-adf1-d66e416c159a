#!/usr/bin/env python3
"""
SSD制造测试CLI工具主入口
"""

import click
import sys
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from cli.commands.device import device
from cli.commands.ft1 import ft1
from cli.commands.ft2 import ft2
from cli.commands.logs import logs
from cli.commands.batch import batch
from cli.commands.config import config
from cli.utils.validators import validate_output_format

console = Console()

@click.group()
@click.option('--config-file', '-c', default='config.yaml', 
              help='配置文件路径', show_default=True)
@click.option('--output-format', '-f', default='table', 
              type=click.Choice(['json', 'csv', 'table'], case_sensitive=False),
              callback=validate_output_format,
              help='输出格式', show_default=True)
@click.option('--verbose', '-v', is_flag=True, help='详细输出模式')
@click.option('--quiet', '-q', is_flag=True, help='静默模式（仅输出结果）')
@click.option('--no-color', is_flag=True, help='禁用彩色输出')
@click.version_option(version='0.1.0', prog_name='ssd-tool')
@click.pass_context
def cli(ctx, config_file, output_format, verbose, quiet, no_color):
    """
    🔧 SSD制造测试CLI工具 v0.1.0
    
    企业级SSD量产命令行工具，支持FT1/FT2测试、设备管理、日志查看和批处理自动化。
    
    \b
    主要功能：
    • 设备管理和状态监控
    • FT1硬件基础测试
    • FT2完整功能测试  
    • 日志查看和分析
    • 批处理自动化
    • 配置管理
    
    \b
    使用示例：
      ssd-tool device list                    # 列出所有设备
      ssd-tool ft1 start DEV001              # 启动FT1测试
      ssd-tool logs view --type ERROR        # 查看错误日志
      ssd-tool batch run config.yaml        # 运行批处理任务
    
    更多帮助请使用: ssd-tool COMMAND --help
    """
    # 确保上下文对象存在
    ctx.ensure_object(dict)
    
    # 存储全局配置
    ctx.obj['config_file'] = config_file
    ctx.obj['output_format'] = output_format.lower()
    ctx.obj['verbose'] = verbose
    ctx.obj['quiet'] = quiet
    ctx.obj['no_color'] = no_color
    
    # 配置控制台
    if no_color:
        console._color_system = None
    
    # 显示欢迎信息（非静默模式）
    if not quiet:
        welcome_text = Text("SSD制造测试CLI工具 v0.1.0", style="bold green")
        console.print(Panel(welcome_text, title="🔧 欢迎使用", border_style="blue"))

# 注册命令组
cli.add_command(device)
cli.add_command(ft1)
cli.add_command(ft2)
cli.add_command(logs)
cli.add_command(batch)
cli.add_command(config)

def main():
    """主函数入口"""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️  用户中断操作[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]❌ 应用启动失败: {e}[/red]")
        if '--verbose' in sys.argv or '-v' in sys.argv:
            console.print_exception()
        sys.exit(1)

if __name__ == '__main__':
    main()
