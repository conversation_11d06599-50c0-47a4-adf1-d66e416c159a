"""
JSON格式化器
"""

import json
from typing import Any, Dict, List
from datetime import datetime

class JsonFormatter:
    """JSON输出格式化器"""
    
    def format(self, data: Any, **kwargs) -> str:
        """
        将数据格式化为JSON字符串
        
        Args:
            data: 要格式化的数据
            **kwargs: 额外参数
                - indent: 缩进空格数，默认为2
                - ensure_ascii: 是否确保ASCII编码，默认为False
                
        Returns:
            格式化后的JSON字符串
        """
        indent = kwargs.get('indent', 2)
        ensure_ascii = kwargs.get('ensure_ascii', False)
        
        # 处理特殊类型
        processed_data = self._process_data(data)
        
        return json.dumps(
            processed_data,
            indent=indent,
            ensure_ascii=ensure_ascii,
            default=self._json_serializer
        )
    
    def _process_data(self, data: Any) -> Any:
        """
        预处理数据，处理特殊类型
        
        Args:
            data: 原始数据
            
        Returns:
            处理后的数据
        """
        if isinstance(data, datetime):
            return data.isoformat()
        elif isinstance(data, dict):
            return {key: self._process_data(value) for key, value in data.items()}
        elif isinstance(data, (list, tuple)):
            return [self._process_data(item) for item in data]
        else:
            return data
    
    def _json_serializer(self, obj: Any) -> str:
        """
        JSON序列化器，处理不能直接序列化的对象
        
        Args:
            obj: 要序列化的对象
            
        Returns:
            序列化后的字符串
        """
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    def format_list(self, items: List[Dict], **kwargs) -> str:
        """
        格式化列表数据
        
        Args:
            items: 数据列表
            **kwargs: 额外参数
            
        Returns:
            格式化后的JSON字符串
        """
        return self.format(items, **kwargs)
    
    def format_single(self, item: Dict, **kwargs) -> str:
        """
        格式化单个数据项
        
        Args:
            item: 单个数据项
            **kwargs: 额外参数
            
        Returns:
            格式化后的JSON字符串
        """
        return self.format(item, **kwargs)
