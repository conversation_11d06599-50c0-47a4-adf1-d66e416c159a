"""
输出格式化器模块
支持JSON、CSV、表格等多种输出格式
"""

from .json_formatter import JsonFormatter
from .csv_formatter import CsvFormatter
from .table_formatter import TableFormatter

__all__ = ['JsonFormatter', 'CsvFormatter', 'TableFormatter', 'get_formatter']

def get_formatter(format_type: str):
    """
    获取指定类型的格式化器
    
    Args:
        format_type: 格式类型 ('json', 'csv', 'table')
        
    Returns:
        对应的格式化器实例
        
    Raises:
        ValueError: 不支持的格式类型
    """
    formatters = {
        'json': JsonFormatter(),
        'csv': CsvFormatter(),
        'table': TableFormatter()
    }
    
    if format_type not in formatters:
        raise ValueError(f"不支持的输出格式: {format_type}")
    
    return formatters[format_type]
