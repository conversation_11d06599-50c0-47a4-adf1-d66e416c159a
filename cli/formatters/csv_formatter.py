"""
CSV格式化器
"""

import csv
import io
from typing import Any, Dict, List

class CsvFormatter:
    """CSV输出格式化器"""
    
    def format(self, data: Any, **kwargs) -> str:
        """
        将数据格式化为CSV字符串
        
        Args:
            data: 要格式化的数据
            **kwargs: 额外参数
                - delimiter: 分隔符，默认为逗号
                - include_headers: 是否包含表头，默认为True
                
        Returns:
            格式化后的CSV字符串
        """
        delimiter = kwargs.get('delimiter', ',')
        include_headers = kwargs.get('include_headers', True)
        
        if not data:
            return ""
        
        # 确保数据是列表格式
        if isinstance(data, dict):
            data = [data]
        elif not isinstance(data, list):
            data = [{'value': str(data)}]
        
        return self._format_list_to_csv(data, delimiter, include_headers)
    
    def _format_list_to_csv(self, items: List[Dict], delimiter: str, include_headers: bool) -> str:
        """
        将字典列表格式化为CSV
        
        Args:
            items: 字典列表
            delimiter: 分隔符
            include_headers: 是否包含表头
            
        Returns:
            CSV字符串
        """
        if not items:
            return ""
        
        # 获取所有可能的字段名
        fieldnames = set()
        for item in items:
            if isinstance(item, dict):
                fieldnames.update(item.keys())
        
        fieldnames = sorted(list(fieldnames))
        
        # 使用StringIO创建CSV
        output = io.StringIO()
        writer = csv.DictWriter(
            output, 
            fieldnames=fieldnames, 
            delimiter=delimiter,
            quoting=csv.QUOTE_MINIMAL
        )
        
        # 写入表头
        if include_headers:
            writer.writeheader()
        
        # 写入数据行
        for item in items:
            if isinstance(item, dict):
                # 处理嵌套对象和特殊类型
                processed_item = self._process_row(item)
                writer.writerow(processed_item)
            else:
                # 非字典类型，转换为字符串
                writer.writerow({'value': str(item)})
        
        return output.getvalue()
    
    def _process_row(self, row: Dict) -> Dict:
        """
        处理单行数据，将复杂类型转换为字符串
        
        Args:
            row: 行数据字典
            
        Returns:
            处理后的行数据
        """
        processed_row = {}
        for key, value in row.items():
            if isinstance(value, (dict, list)):
                # 复杂类型转换为JSON字符串
                import json
                processed_row[key] = json.dumps(value, ensure_ascii=False)
            elif value is None:
                processed_row[key] = ""
            else:
                processed_row[key] = str(value)
        
        return processed_row
    
    def format_list(self, items: List[Dict], **kwargs) -> str:
        """
        格式化列表数据
        
        Args:
            items: 数据列表
            **kwargs: 额外参数
            
        Returns:
            格式化后的CSV字符串
        """
        return self.format(items, **kwargs)
    
    def format_single(self, item: Dict, **kwargs) -> str:
        """
        格式化单个数据项
        
        Args:
            item: 单个数据项
            **kwargs: 额外参数
            
        Returns:
            格式化后的CSV字符串
        """
        return self.format([item], **kwargs)
