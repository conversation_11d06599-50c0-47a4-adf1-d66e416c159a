"""
表格格式化器
"""

from typing import Any, Dict, List, Optional
from rich.console import Console
from rich.table import Table
from rich.text import Text
import io

class TableFormatter:
    """表格输出格式化器"""
    
    def __init__(self):
        self.console = Console(file=io.StringIO(), width=120)
    
    def format(self, data: Any, **kwargs) -> str:
        """
        将数据格式化为表格字符串
        
        Args:
            data: 要格式化的数据
            **kwargs: 额外参数
                - title: 表格标题
                - show_header: 是否显示表头，默认为True
                - show_lines: 是否显示分割线，默认为False
                
        Returns:
            格式化后的表格字符串
        """
        title = kwargs.get('title')
        show_header = kwargs.get('show_header', True)
        show_lines = kwargs.get('show_lines', False)
        
        if not data:
            return "无数据"
        
        # 确保数据是列表格式
        if isinstance(data, dict):
            data = [data]
        elif not isinstance(data, list):
            data = [{'value': str(data)}]
        
        return self._create_table(data, title, show_header, show_lines)
    
    def _create_table(self, items: List[Dict], title: Optional[str], 
                     show_header: bool, show_lines: bool) -> str:
        """
        创建Rich表格
        
        Args:
            items: 数据列表
            title: 表格标题
            show_header: 是否显示表头
            show_lines: 是否显示分割线
            
        Returns:
            表格字符串
        """
        if not items:
            return "无数据"
        
        # 获取所有字段名
        fieldnames = set()
        for item in items:
            if isinstance(item, dict):
                fieldnames.update(item.keys())
        
        fieldnames = sorted(list(fieldnames))
        
        # 创建表格
        table = Table(
            title=title,
            show_header=show_header,
            show_lines=show_lines,
            header_style="bold magenta"
        )
        
        # 添加列
        for field in fieldnames:
            table.add_column(
                self._format_column_name(field),
                style="cyan",
                no_wrap=False
            )
        
        # 添加数据行
        for item in items:
            if isinstance(item, dict):
                row_data = []
                for field in fieldnames:
                    value = item.get(field, "")
                    formatted_value = self._format_cell_value(value)
                    row_data.append(formatted_value)
                table.add_row(*row_data)
            else:
                table.add_row(str(item))
        
        # 渲染表格到字符串
        console = Console(file=io.StringIO(), width=120)
        console.print(table)
        return console.file.getvalue()
    
    def _format_column_name(self, name: str) -> str:
        """
        格式化列名
        
        Args:
            name: 原始列名
            
        Returns:
            格式化后的列名
        """
        # 将下划线替换为空格，首字母大写
        return name.replace('_', ' ').title()
    
    def _format_cell_value(self, value: Any) -> str:
        """
        格式化单元格值
        
        Args:
            value: 原始值
            
        Returns:
            格式化后的字符串
        """
        if value is None:
            return "[dim]N/A[/dim]"
        elif isinstance(value, bool):
            return "[green]✓[/green]" if value else "[red]✗[/red]"
        elif isinstance(value, (dict, list)):
            # 复杂类型简化显示
            if isinstance(value, dict) and len(value) <= 3:
                return ", ".join(f"{k}: {v}" for k, v in value.items())
            elif isinstance(value, list) and len(value) <= 3:
                return ", ".join(str(v) for v in value)
            else:
                return f"[dim]{type(value).__name__}({len(value)} items)[/dim]"
        else:
            return str(value)
    
    def format_list(self, items: List[Dict], **kwargs) -> str:
        """
        格式化列表数据
        
        Args:
            items: 数据列表
            **kwargs: 额外参数
            
        Returns:
            格式化后的表格字符串
        """
        return self.format(items, **kwargs)
    
    def format_single(self, item: Dict, **kwargs) -> str:
        """
        格式化单个数据项（垂直布局）
        
        Args:
            item: 单个数据项
            **kwargs: 额外参数
            
        Returns:
            格式化后的表格字符串
        """
        if not isinstance(item, dict):
            return str(item)
        
        # 创建垂直表格
        table = Table(
            title=kwargs.get('title'),
            show_header=True,
            header_style="bold magenta"
        )
        
        table.add_column("属性", style="cyan", no_wrap=True)
        table.add_column("值", style="white", no_wrap=False)
        
        for key, value in item.items():
            formatted_key = self._format_column_name(key)
            formatted_value = self._format_cell_value(value)
            table.add_row(formatted_key, formatted_value)
        
        # 渲染表格
        console = Console(file=io.StringIO(), width=120)
        console.print(table)
        return console.file.getvalue()
