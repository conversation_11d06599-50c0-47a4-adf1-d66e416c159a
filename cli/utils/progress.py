"""
进度管理器
使用Rich库提供美观的进度条显示
"""

import time
from typing import Optional, Callable, Any
from rich.console import Console
from rich.progress import (
    Progress, 
    SpinnerColumn, 
    TextColumn, 
    BarColumn, 
    TaskProgressColumn,
    TimeRemainingColumn,
    TimeElapsedColumn
)
from rich.live import Live
from rich.panel import Panel
from rich.text import Text

console = Console()

class ProgressManager:
    """进度管理器，提供各种进度显示功能"""
    
    def __init__(self, console: Optional[Console] = None):
        self.console = console or Console()
        self.progress = None
        self.live = None
    
    def create_progress_bar(self, description: str = "处理中...") -> Progress:
        """
        创建标准进度条
        
        Args:
            description: 进度描述
            
        Returns:
            Progress对象
        """
        return Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            TimeRemainingColumn(),
            TimeElapsedColumn(),
            console=self.console
        )
    
    def create_spinner(self, description: str = "处理中...") -> Progress:
        """
        创建旋转器（无确定进度）
        
        Args:
            description: 进度描述
            
        Returns:
            Progress对象
        """
        return Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        )
    
    def show_progress(self, total: int, description: str = "处理中...", 
                     callback: Optional[Callable] = None) -> Any:
        """
        显示进度条并执行回调函数
        
        Args:
            total: 总步数
            description: 进度描述
            callback: 回调函数，应该接受progress和task_id参数
            
        Returns:
            回调函数的返回值
        """
        with self.create_progress_bar(description) as progress:
            task_id = progress.add_task(description, total=total)
            
            if callback:
                return callback(progress, task_id)
            else:
                # 默认行为：简单的进度演示
                for i in range(total):
                    time.sleep(0.1)  # 模拟工作
                    progress.update(task_id, advance=1)
    
    def show_spinner(self, description: str = "处理中...", 
                    callback: Optional[Callable] = None) -> Any:
        """
        显示旋转器并执行回调函数
        
        Args:
            description: 进度描述
            callback: 回调函数，应该接受progress和task_id参数
            
        Returns:
            回调函数的返回值
        """
        with self.create_spinner(description) as progress:
            task_id = progress.add_task(description)
            
            if callback:
                return callback(progress, task_id)
            else:
                # 默认行为：旋转3秒
                for _ in range(30):
                    time.sleep(0.1)
                    progress.update(task_id)
    
    def show_status(self, message: str, status: str = "info"):
        """
        显示状态消息
        
        Args:
            message: 状态消息
            status: 状态类型 ('info', 'success', 'warning', 'error')
        """
        status_styles = {
            'info': ('ℹ️', 'blue'),
            'success': ('✅', 'green'),
            'warning': ('⚠️', 'yellow'),
            'error': ('❌', 'red')
        }
        
        icon, color = status_styles.get(status, ('ℹ️', 'blue'))
        self.console.print(f"{icon} [{color}]{message}[/{color}]")
    
    def show_panel(self, content: str, title: str = "", style: str = "blue"):
        """
        显示面板消息
        
        Args:
            content: 面板内容
            title: 面板标题
            style: 面板样式
        """
        panel = Panel(
            Text(content),
            title=title,
            border_style=style
        )
        self.console.print(panel)
    
    def confirm(self, message: str, default: bool = False) -> bool:
        """
        显示确认对话框
        
        Args:
            message: 确认消息
            default: 默认选择
            
        Returns:
            用户选择结果
        """
        default_text = "Y/n" if default else "y/N"
        prompt = f"{message} [{default_text}]: "
        
        try:
            response = input(prompt).strip().lower()
            if not response:
                return default
            return response in ('y', 'yes', '是', 'true', '1')
        except KeyboardInterrupt:
            self.console.print("\n[yellow]操作已取消[/yellow]")
            return False
    
    def multi_progress_demo(self):
        """多任务进度演示"""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            TimeRemainingColumn(),
            console=self.console
        ) as progress:
            
            # 添加多个任务
            task1 = progress.add_task("FT1测试", total=100)
            task2 = progress.add_task("FT2测试", total=80)
            task3 = progress.add_task("数据处理", total=60)
            
            # 模拟并发执行
            for i in range(100):
                time.sleep(0.02)
                if i < 100:
                    progress.update(task1, advance=1)
                if i < 80:
                    progress.update(task2, advance=1)
                if i < 60:
                    progress.update(task3, advance=1)

# 全局进度管理器实例
progress_manager = ProgressManager()
