"""
命令行参数验证器
"""

import re
import click
from typing import Any, Optional
from pathlib import Path

def validate_output_format(ctx: click.Context, param: click.Parameter, value: str) -> str:
    """
    验证输出格式参数
    
    Args:
        ctx: Click上下文
        param: 参数对象
        value: 参数值
        
    Returns:
        验证后的参数值
        
    Raises:
        click.BadParameter: 参数无效时抛出
    """
    if value is None:
        return value
    
    valid_formats = ['json', 'csv', 'table']
    if value.lower() not in valid_formats:
        raise click.BadParameter(
            f"无效的输出格式 '{value}'。支持的格式: {', '.join(valid_formats)}"
        )
    
    return value.lower()

def validate_device_id(ctx: click.Context, param: click.Parameter, value: str) -> str:
    """
    验证设备ID参数
    
    Args:
        ctx: Click上下文
        param: 参数对象
        value: 参数值
        
    Returns:
        验证后的参数值
        
    Raises:
        click.BadParameter: 参数无效时抛出
    """
    if value is None:
        return value
    
    # 设备ID格式：DEV + 3位数字，如 DEV001
    pattern = r'^DEV\d{3}$'
    if not re.match(pattern, value.upper()):
        raise click.BadParameter(
            f"无效的设备ID格式 '{value}'。正确格式: DEV001, DEV002, ..."
        )
    
    return value.upper()

def validate_file_path(ctx: click.Context, param: click.Parameter, value: str) -> Optional[Path]:
    """
    验证文件路径参数
    
    Args:
        ctx: Click上下文
        param: 参数对象
        value: 参数值
        
    Returns:
        验证后的Path对象
        
    Raises:
        click.BadParameter: 参数无效时抛出
    """
    if value is None:
        return None
    
    path = Path(value)
    
    # 检查文件是否存在
    if not path.exists():
        raise click.BadParameter(f"文件不存在: {value}")
    
    # 检查是否为文件
    if not path.is_file():
        raise click.BadParameter(f"路径不是文件: {value}")
    
    # 检查文件是否可读
    if not path.is_file() or not path.stat().st_size >= 0:
        raise click.BadParameter(f"文件不可读: {value}")
    
    return path

def validate_config_file(ctx: click.Context, param: click.Parameter, value: str) -> Optional[Path]:
    """
    验证配置文件参数
    
    Args:
        ctx: Click上下文
        param: 参数对象
        value: 参数值
        
    Returns:
        验证后的Path对象
        
    Raises:
        click.BadParameter: 参数无效时抛出
    """
    if value is None:
        return None
    
    path = Path(value)
    
    # 如果文件不存在，检查是否可以创建
    if not path.exists():
        try:
            # 检查父目录是否存在
            if not path.parent.exists():
                raise click.BadParameter(f"配置文件目录不存在: {path.parent}")
            
            # 检查父目录是否可写
            if not path.parent.is_dir():
                raise click.BadParameter(f"配置文件路径不是目录: {path.parent}")
                
        except PermissionError:
            raise click.BadParameter(f"没有权限访问配置文件路径: {path}")
    
    # 检查文件扩展名
    valid_extensions = ['.yaml', '.yml', '.json']
    if path.suffix.lower() not in valid_extensions:
        raise click.BadParameter(
            f"不支持的配置文件格式 '{path.suffix}'。支持的格式: {', '.join(valid_extensions)}"
        )
    
    return path

def validate_log_level(ctx: click.Context, param: click.Parameter, value: str) -> str:
    """
    验证日志级别参数
    
    Args:
        ctx: Click上下文
        param: 参数对象
        value: 参数值
        
    Returns:
        验证后的参数值
        
    Raises:
        click.BadParameter: 参数无效时抛出
    """
    if value is None:
        return value
    
    valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    if value.upper() not in valid_levels:
        raise click.BadParameter(
            f"无效的日志级别 '{value}'。支持的级别: {', '.join(valid_levels)}"
        )
    
    return value.upper()

def validate_positive_integer(ctx: click.Context, param: click.Parameter, value: Any) -> int:
    """
    验证正整数参数
    
    Args:
        ctx: Click上下文
        param: 参数对象
        value: 参数值
        
    Returns:
        验证后的整数值
        
    Raises:
        click.BadParameter: 参数无效时抛出
    """
    if value is None:
        return value
    
    try:
        int_value = int(value)
        if int_value <= 0:
            raise click.BadParameter(f"参数必须是正整数，得到: {value}")
        return int_value
    except ValueError:
        raise click.BadParameter(f"参数必须是整数，得到: {value}")

def validate_timeout(ctx: click.Context, param: click.Parameter, value: Any) -> int:
    """
    验证超时参数（秒）
    
    Args:
        ctx: Click上下文
        param: 参数对象
        value: 参数值
        
    Returns:
        验证后的超时值
        
    Raises:
        click.BadParameter: 参数无效时抛出
    """
    if value is None:
        return 30  # 默认30秒
    
    try:
        timeout_value = int(value)
        if timeout_value < 1 or timeout_value > 3600:  # 1秒到1小时
            raise click.BadParameter(f"超时时间必须在1-3600秒之间，得到: {value}")
        return timeout_value
    except ValueError:
        raise click.BadParameter(f"超时时间必须是整数，得到: {value}")

def validate_parallel_count(ctx: click.Context, param: click.Parameter, value: Any) -> int:
    """
    验证并行数量参数
    
    Args:
        ctx: Click上下文
        param: 参数对象
        value: 参数值
        
    Returns:
        验证后的并行数量
        
    Raises:
        click.BadParameter: 参数无效时抛出
    """
    if value is None:
        return 1  # 默认单线程
    
    try:
        parallel_value = int(value)
        if parallel_value < 1 or parallel_value > 16:  # 1-16个并行任务
            raise click.BadParameter(f"并行数量必须在1-16之间，得到: {value}")
        return parallel_value
    except ValueError:
        raise click.BadParameter(f"并行数量必须是整数，得到: {value}")
