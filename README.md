# SSD制造测试工具 (mp_tools)

## 🎯 项目简介

企业级SSD量产桌面软件系统，支持FT1（硬件基础测试）和FT2（完整功能测试）两个关键测试阶段，实现从硬件验证到最终产品交付的全流程自动化管理。

## ✨ 主要特性

- 🖥️ **直观的桌面GUI界面** - 基于PySide6的现代化界面
- 🔧 **双阶段测试支持** - FT1硬件测试 + FT2功能测试
- 💾 **本地化数据存储** - SQLite数据库，确保数据安全
- 📊 **完整的日志系统** - 多类型日志查看、过滤、搜索
- 🚀 **多设备并发** - 支持同时测试多个SSD设备
- 📦 **一键打包部署** - PyInstaller打包成独立可执行文件

## 🛠️ 技术栈

- **GUI框架**: PySide6 + Qt Designer
- **核心语言**: Python 3.9+
- **数据库**: SQLite + SQLAlchemy
- **硬件通信**: pySerial + PyNvme + RPi.GPIO
- **数据处理**: pandas + numpy
- **打包工具**: PyInstaller

## 📁 项目结构

```
mp_tools/                     # 项目根目录
├── main.py                   # 应用入口
├── requirements.txt          # 依赖包列表
├── config.json              # 配置文件
├── doc/                     # 📚 项目文档
├── gui/                     # 🖥️ 图形界面模块
├── core/                    # ⚙️ 核心业务模块
├── hardware/                # 🔌 硬件接口模块
├── models/                  # 📊 数据模型
├── utils/                   # 🛠️ 工具模块
├── build/                   # 📦 打包配置
├── tests/                   # 🧪 测试模块
└── data/                    # 💾 数据目录
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Windows 10/11, Ubuntu 20.04+, 或 CentOS 8+
- 最低4GB内存，推荐8GB+

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python main.py
```

### 打包应用
```bash
python build/build_spec.py
```

## 📚 文档

详细文档请查看 [doc/](./doc/) 目录：

- **[项目文档索引](./doc/README.md)** - 完整文档导航
- **[系统架构设计](./doc/SSD_Manufacturing_System_Architecture_Overview.md)** - 技术架构和模块设计
- **[迭代开发计划](./doc/SSD_Manufacturing_Tool_Iterative_Development_Plan.md)** - 6阶段开发路线图

## 🔄 开发进度

### 当前状态：第一阶段准备中

- ✅ 系统架构设计完成
- ✅ 迭代开发计划制定完成
- ✅ 项目结构规划完成
- ⏳ 准备开始第一阶段开发（最简可运行界面）

### 开发阶段规划

| 阶段 | 时间 | 主要功能 | 状态 |
|------|------|----------|------|
| 第一阶段 | 第1周 | 基础GUI界面 | ⏳ 准备中 |
| 第二阶段 | 第2周 | 数据管理功能 | ⏸️ 待开始 |
| 第三阶段 | 第3周 | 日志查看系统 | ⏸️ 待开始 |
| 第四阶段 | 第4-5周 | FT1测试框架 | ⏸️ 待开始 |
| 第五阶段 | 第6-7周 | FT2测试框架 | ⏸️ 待开始 |
| 第六阶段 | 第8-10周 | 硬件集成优化 | ⏸️ 待开始 |

## 🧪 测试流程

### FT1测试流程（硬件基础测试）
```
设备检测 → 固件烧写 → 硬件测试启动 → ACTIVITY管脚监控 → 结果解析 → 数据存储
```

### FT2测试流程（完整功能测试）
```
NVMe设备连接 → FT固件下载 → NAND burn-in测试 → SLT系统测试 → 产品信息写入 → 最终验证
```

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- **项目负责人**: [待填写]
- **开发团队**: [待填写]
- **问题反馈**: [GitHub Issues](../../issues)

---

**项目版本**: v0.1.0-dev
**创建时间**: 2025-07-30
**最后更新**: 2025-07-30
